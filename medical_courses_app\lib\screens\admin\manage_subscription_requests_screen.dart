import 'package:flutter/material.dart';
import '../../utils/constants.dart';

/// شاشة إدارة طلبات الاشتراك
class ManageSubscriptionRequestsScreen extends StatelessWidget {
  const ManageSubscriptionRequestsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة طلبات الاشتراك'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.approval,
              size: 64,
              color: AppColors.primaryBlue,
            ),
            SizedBox(height: AppDimensions.paddingM),
            Text(
              'هذه شاشة إدارة طلبات الاشتراك',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.darkText,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.paddingS),
            Text(
              'سيتم عرض قائمة طلبات الاشتراك وإدارتها هنا',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
