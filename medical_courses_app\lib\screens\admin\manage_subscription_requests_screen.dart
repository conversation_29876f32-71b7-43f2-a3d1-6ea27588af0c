import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../utils/constants.dart';
import '../../providers/auth_provider.dart';

/// شاشة إدارة طلبات الاشتراك
class ManageSubscriptionRequestsScreen extends ConsumerWidget {
  const ManageSubscriptionRequestsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة طلبات الاشتراك'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            print('DEBUG: Back button pressed on ${runtimeType.toString()}.');
            if (context.canPop()) {
              context.pop();
              print('DEBUG: context.pop() called.');
            } else {
              final authState = ref.read(authProvider);
              if (authState.isAuthenticated && authState.user != null) {
                context.go(authState.user!.isAdmin ? '/admin' : '/student');
                print(
                    'DEBUG: No screen to pop, redirecting to home based on role.');
              } else {
                context.go('/login');
                print('DEBUG: No screen to pop, redirecting to login.');
              }
            }
          },
        ),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.approval, size: 64, color: AppColors.primaryBlue),
            SizedBox(height: AppDimensions.paddingM),
            Text(
              'هذه شاشة إدارة طلبات الاشتراك',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.darkText,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.paddingS),
            Text(
              'سيتم عرض قائمة طلبات الاشتراك وإدارتها هنا',
              style: TextStyle(fontSize: 14, color: AppColors.lightText),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
