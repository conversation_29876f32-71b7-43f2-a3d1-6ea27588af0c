import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../utils/constants.dart';
import '../../models/lecture_content.dart';

/// نموذج إضافة/تعديل سؤال MCQ
class McqFormDialog extends ConsumerStatefulWidget {
  final QuizQuestion? question;
  final int? index;

  const McqFormDialog({
    super.key,
    this.question,
    this.index,
  });

  @override
  ConsumerState<McqFormDialog> createState() => _McqFormDialogState();
}

class _McqFormDialogState extends ConsumerState<McqFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _questionController = TextEditingController();
  final _explanationController = TextEditingController();
  
  List<TextEditingController> _optionControllers = [];
  int _correctAnswer = 0;
  int _optionsCount = 4;

  bool get isEditing => widget.question != null;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    
    if (isEditing) {
      _questionController.text = widget.question!.question;
      _explanationController.text = widget.question!.explanation ?? '';
      _correctAnswer = widget.question!.correctAnswer;
      _optionsCount = widget.question!.options.length;
      
      // إعادة تهيئة المتحكمات بناءً على عدد الخيارات
      _initializeControllers();
      
      // ملء الخيارات
      for (int i = 0; i < widget.question!.options.length; i++) {
        if (i < _optionControllers.length) {
          _optionControllers[i].text = widget.question!.options[i];
        }
      }
    }
  }

  void _initializeControllers() {
    // تنظيف المتحكمات القديمة
    for (var controller in _optionControllers) {
      controller.dispose();
    }
    
    // إنشاء متحكمات جديدة
    _optionControllers = List.generate(
      _optionsCount,
      (index) => TextEditingController(),
    );
  }

  @override
  void dispose() {
    _questionController.dispose();
    _explanationController.dispose();
    for (var controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(isEditing ? 'تعديل سؤال الاختبار' : 'إضافة سؤال اختبار جديد'),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // السؤال
                TextFormField(
                  controller: _questionController,
                  decoration: const InputDecoration(
                    labelText: 'نص السؤال *',
                    hintText: 'اكتب السؤال هنا',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال نص السؤال';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: AppDimensions.paddingM),
                
                // عدد الخيارات
                Row(
                  children: [
                    const Text('عدد الخيارات:'),
                    const SizedBox(width: AppDimensions.paddingM),
                    DropdownButton<int>(
                      value: _optionsCount,
                      items: [2, 3, 4, 5, 6].map((count) {
                        return DropdownMenuItem<int>(
                          value: count,
                          child: Text('$count خيارات'),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null && value != _optionsCount) {
                          setState(() {
                            _optionsCount = value;
                            _correctAnswer = 0; // إعادة تعيين الإجابة الصحيحة
                            _initializeControllers();
                          });
                        }
                      },
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.paddingM),
                
                // الخيارات
                const Text(
                  'الخيارات:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: AppDimensions.paddingS),
                
                ...List.generate(_optionsCount, (index) {
                  return Padding(
                    padding: const EdgeInsets.only(bottom: AppDimensions.paddingS),
                    child: Row(
                      children: [
                        // راديو للإجابة الصحيحة
                        Radio<int>(
                          value: index,
                          groupValue: _correctAnswer,
                          onChanged: (value) {
                            setState(() {
                              _correctAnswer = value!;
                            });
                          },
                        ),
                        // حقل الخيار
                        Expanded(
                          child: TextFormField(
                            controller: _optionControllers[index],
                            decoration: InputDecoration(
                              labelText: 'الخيار ${index + 1} *',
                              hintText: 'اكتب الخيار هنا',
                              border: const OutlineInputBorder(),
                              prefixIcon: _correctAnswer == index
                                  ? const Icon(Icons.check_circle, color: AppColors.medicalGreen)
                                  : const Icon(Icons.radio_button_unchecked),
                            ),
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'يرجى إدخال الخيار ${index + 1}';
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                
                const SizedBox(height: AppDimensions.paddingM),
                
                // الشرح التوضيحي
                TextFormField(
                  controller: _explanationController,
                  decoration: const InputDecoration(
                    labelText: 'الشرح التوضيحي (اختياري)',
                    hintText: 'شرح لماذا هذه الإجابة صحيحة',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                ),
                
                const SizedBox(height: AppDimensions.paddingM),
                
                // معلومات الإجابة الصحيحة
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  decoration: BoxDecoration(
                    color: AppColors.medicalGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                    border: Border.all(color: AppColors.medicalGreen.withValues(alpha: 0.3)),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Row(
                        children: [
                          Icon(Icons.info, color: AppColors.medicalGreen),
                          SizedBox(width: AppDimensions.paddingS),
                          Text(
                            'الإجابة الصحيحة:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: AppColors.medicalGreen,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      Text(
                        'الخيار ${_correctAnswer + 1}: ${_optionControllers.length > _correctAnswer ? _optionControllers[_correctAnswer].text : "غير محدد"}',
                        style: const TextStyle(
                          color: AppColors.medicalGreen,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveQuestion,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
          ),
          child: Text(
            isEditing ? 'تحديث' : 'إضافة',
            style: const TextStyle(color: AppColors.white),
          ),
        ),
      ],
    );
  }

  void _saveQuestion() {
    if (!_formKey.currentState!.validate()) return;

    final question = _questionController.text.trim();
    final explanation = _explanationController.text.trim();
    
    final options = _optionControllers
        .map((controller) => controller.text.trim())
        .toList();

    // التحقق من أن جميع الخيارات مملوءة
    if (options.any((option) => option.isEmpty)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الخيارات'),
          backgroundColor: AppColors.errorRed,
        ),
      );
      return;
    }

    final quizQuestion = QuizQuestion(
      question: question,
      options: options,
      correctAnswer: _correctAnswer,
      explanation: explanation.isEmpty ? null : explanation,
    );

    Navigator.of(context).pop(quizQuestion);
  }
}
