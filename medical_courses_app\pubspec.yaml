name: medical_courses_app
description: "تطبيق الدورات الطبية - منصة تعليمية متقدمة للمحتوى الطبي"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # Localization support
  flutter_localizations:
    sdk: flutter

  # Firebase dependencies
  firebase_core: ^3.8.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0
  firebase_storage: ^12.3.6

  # State management
  flutter_riverpod: ^2.6.1

  # Navigation
  go_router: ^14.6.2

  # Media players
  video_player: ^2.9.2
  chewie: ^1.8.5

  # PDF viewer
  flutter_pdfview: ^1.3.3

  # Internationalization
  intl: ^0.20.1

  # Image picker
  image_picker: ^1.1.2

  # Security features
  # flutter_windowmanager: ^0.2.0 # تم تعطيله مؤقت<|im_start|> لحل مشاكل البناء
  # flutter_jailbreak_detection: ^1.10.0 # تم تعطيله مؤقت<|im_start|> لحل مشاكل البناء

  # UI components
  cupertino_icons: ^1.0.8

  # File handling
  file_picker: ^8.1.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets for the application
  assets:
    - assets/images/
    - assets/icons/
    - assets/fonts/

  # Custom fonts for Arabic and English support
  # TODO: إضافة ملفات الخطوط العربية لاحقاً
  # fonts:
  #   - family: NotoSansArabic
  #     fonts:
  #       - asset: assets/fonts/NotoSansArabic-Regular.ttf
  #       - asset: assets/fonts/NotoSansArabic-Bold.ttf
  #         weight: 700
