import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:file_picker/file_picker.dart';
import '../models/year.dart';
import '../models/subject.dart';
import '../models/course.dart';
import '../models/lecture.dart';
import '../models/lecture_content.dart';

/// خدمة إدارة المحتوى للمشرف
class AdminService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // مراجع المجموعات
  CollectionReference get _yearsRef => _firestore.collection('years');
  CollectionReference get _subjectsRef => _firestore.collection('subjects');
  CollectionReference get _coursesRef => _firestore.collection('courses');
  CollectionReference get _lecturesRef => _firestore.collection('lectures');
  CollectionReference get _lectureContentsRef =>
      _firestore.collection('lectureContents');

  // ==================== إدارة السنوات ====================

  /// إضافة سنة جديدة
  Future<void> addYear(Year year) async {
    try {
      await _yearsRef.doc(year.id).set(year.toFirestore());
    } catch (e) {
      throw Exception('فشل في إضافة السنة: $e');
    }
  }

  /// تحديث سنة
  Future<void> updateYear(Year year) async {
    try {
      await _yearsRef.doc(year.id).update(year.toFirestore());
    } catch (e) {
      throw Exception('فشل في تحديث السنة: $e');
    }
  }

  /// حذف سنة
  Future<void> deleteYear(String id) async {
    try {
      // حذف جميع المواد المرتبطة بالسنة
      final subjects = await _subjectsRef.where('yearId', isEqualTo: id).get();
      for (final doc in subjects.docs) {
        await deleteSubject(doc.id);
      }

      // حذف السنة
      await _yearsRef.doc(id).delete();
    } catch (e) {
      throw Exception('فشل في حذف السنة: $e');
    }
  }

  /// جلب جميع السنوات
  Stream<List<Year>> getYears() {
    return _yearsRef.orderBy('order', descending: false).snapshots().map(
        (snapshot) =>
            snapshot.docs.map((doc) => Year.fromFirestore(doc)).toList());
  }

  /// جلب سنة محددة
  Future<Year?> getYear(String id) async {
    try {
      final doc = await _yearsRef.doc(id).get();
      if (doc.exists) {
        return Year.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب السنة: $e');
    }
  }

  // ==================== إدارة المواد ====================

  /// إضافة مادة جديدة
  Future<void> addSubject(Subject subject) async {
    try {
      await _subjectsRef.doc(subject.id).set(subject.toFirestore());
    } catch (e) {
      throw Exception('فشل في إضافة المادة: $e');
    }
  }

  /// تحديث مادة
  Future<void> updateSubject(Subject subject) async {
    try {
      await _subjectsRef.doc(subject.id).update(subject.toFirestore());
    } catch (e) {
      throw Exception('فشل في تحديث المادة: $e');
    }
  }

  /// حذف مادة
  Future<void> deleteSubject(String id) async {
    try {
      // حذف جميع الكورسات المرتبطة بالمادة
      final courses = await _coursesRef.where('subjectId', isEqualTo: id).get();
      for (final doc in courses.docs) {
        await deleteCourse(doc.id);
      }

      // حذف المادة
      await _subjectsRef.doc(id).delete();
    } catch (e) {
      throw Exception('فشل في حذف المادة: $e');
    }
  }

  /// جلب مواد سنة محددة
  Stream<List<Subject>> getSubjects(String yearId) {
    return _subjectsRef
        .where('yearId', isEqualTo: yearId)
        .orderBy('order', descending: false)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => Subject.fromFirestore(doc)).toList());
  }

  /// جلب مادة محددة
  Future<Subject?> getSubject(String id) async {
    try {
      final doc = await _subjectsRef.doc(id).get();
      if (doc.exists) {
        return Subject.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب المادة: $e');
    }
  }

  // ==================== إدارة الكورسات ====================

  /// إضافة كورس جديد
  Future<void> addCourse(Course course) async {
    try {
      await _coursesRef.doc(course.id).set(course.toFirestore());
    } catch (e) {
      throw Exception('فشل في إضافة الكورس: $e');
    }
  }

  /// تحديث كورس
  Future<void> updateCourse(Course course) async {
    try {
      await _coursesRef.doc(course.id).update(course.toFirestore());
    } catch (e) {
      throw Exception('فشل في تحديث الكورس: $e');
    }
  }

  /// حذف كورس
  Future<void> deleteCourse(String id) async {
    try {
      // حذف جميع المحاضرات المرتبطة بالكورس
      final lectures =
          await _lecturesRef.where('courseId', isEqualTo: id).get();
      for (final doc in lectures.docs) {
        await deleteLecture(doc.id);
      }

      // حذف الكورس
      await _coursesRef.doc(id).delete();
    } catch (e) {
      throw Exception('فشل في حذف الكورس: $e');
    }
  }

  /// جلب كورسات مادة محددة
  Stream<List<Course>> getCourses(String subjectId) {
    return _coursesRef
        .where('subjectId', isEqualTo: subjectId)
        .orderBy('order', descending: false)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => Course.fromFirestore(doc)).toList());
  }

  /// جلب كورس محدد
  Future<Course?> getCourse(String id) async {
    try {
      final doc = await _coursesRef.doc(id).get();
      if (doc.exists) {
        return Course.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب الكورس: $e');
    }
  }

  // ==================== إدارة المحاضرات ====================

  /// إضافة محاضرة جديدة
  Future<void> addLecture(Lecture lecture) async {
    try {
      await _lecturesRef.doc(lecture.id).set(lecture.toFirestore());
    } catch (e) {
      throw Exception('فشل في إضافة المحاضرة: $e');
    }
  }

  /// تحديث محاضرة
  Future<void> updateLecture(Lecture lecture) async {
    try {
      await _lecturesRef.doc(lecture.id).update(lecture.toFirestore());
    } catch (e) {
      throw Exception('فشل في تحديث المحاضرة: $e');
    }
  }

  /// حذف محاضرة
  Future<void> deleteLecture(String id) async {
    try {
      // حذف محتوى المحاضرة
      await _lectureContentsRef.doc(id).delete();

      // حذف المحاضرة
      await _lecturesRef.doc(id).delete();
    } catch (e) {
      throw Exception('فشل في حذف المحاضرة: $e');
    }
  }

  /// جلب محاضرات كورس محدد
  Stream<List<Lecture>> getLectures(String courseId) {
    return _lecturesRef
        .where('courseId', isEqualTo: courseId)
        .orderBy('order', descending: false)
        .snapshots()
        .map((snapshot) =>
            snapshot.docs.map((doc) => Lecture.fromFirestore(doc)).toList());
  }

  /// جلب محاضرة محددة
  Future<Lecture?> getLecture(String id) async {
    try {
      final doc = await _lecturesRef.doc(id).get();
      if (doc.exists) {
        return Lecture.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب المحاضرة: $e');
    }
  }

  // ==================== إدارة محتوى المحاضرات ====================

  /// تحديث محتوى المحاضرة
  Future<void> updateLectureContent(LectureContent content) async {
    try {
      await _lectureContentsRef
          .doc(content.lectureId)
          .set(content.toFirestore());
    } catch (e) {
      throw Exception('فشل في تحديث محتوى المحاضرة: $e');
    }
  }

  /// جلب محتوى محاضرة محددة
  Stream<LectureContent?> getLectureContent(String lectureId) {
    return _lectureContentsRef.doc(lectureId).snapshots().map((doc) {
      if (doc.exists) {
        return LectureContent.fromFirestore(doc);
      }
      return null;
    });
  }

  /// جلب محتوى محاضرة محددة (مرة واحدة)
  Future<LectureContent?> getLectureContentOnce(String lectureId) async {
    try {
      final doc = await _lectureContentsRef.doc(lectureId).get();
      if (doc.exists) {
        return LectureContent.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب محتوى المحاضرة: $e');
    }
  }

  // ==================== إدارة الملفات ====================

  /// رفع ملف إلى Firebase Storage
  Future<String> uploadFile(PlatformFile file, String path) async {
    try {
      final ref = _storage.ref().child(path);

      // رفع الملف
      final uploadTask = ref.putFile(File(file.path!));

      // انتظار اكتمال الرفع
      final snapshot = await uploadTask;

      // الحصول على رابط التحميل
      final downloadUrl = await snapshot.ref.getDownloadURL();

      return downloadUrl;
    } catch (e) {
      throw Exception('فشل في رفع الملف: $e');
    }
  }

  /// رفع صورة
  Future<String> uploadImage(
      PlatformFile file, String folder, String fileName) async {
    final path = '$folder/$fileName.${file.extension}';
    return await uploadFile(file, path);
  }

  /// رفع فيديو
  Future<String> uploadVideo(PlatformFile file, String lectureId) async {
    final path = 'videos/$lectureId.${file.extension}';
    return await uploadFile(file, path);
  }

  /// رفع ملف PDF
  Future<String> uploadPDF(PlatformFile file, String lectureId) async {
    final path = 'summaries/$lectureId.pdf';
    return await uploadFile(file, path);
  }

  /// حذف ملف من Storage
  Future<void> deleteFile(String url) async {
    try {
      final ref = _storage.refFromURL(url);
      await ref.delete();
    } catch (e) {
      // تجاهل الخطأ إذا كان الملف غير موجود
      print('تحذير: فشل في حذف الملف: $e');
    }
  }

  // ==================== دوال مساعدة ====================

  /// توليد معرف فريد
  String generateId() {
    return _firestore.collection('temp').doc().id;
  }

  /// التحقق من صحة الملف
  bool isValidFile(PlatformFile file, List<String> allowedExtensions) {
    if (file.extension == null) return false;
    return allowedExtensions.contains(file.extension!.toLowerCase());
  }

  /// التحقق من حجم الملف
  bool isValidFileSize(PlatformFile file, int maxSizeInMB) {
    if (file.size == 0) return false;
    final maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    return file.size <= maxSizeInBytes;
  }
}
