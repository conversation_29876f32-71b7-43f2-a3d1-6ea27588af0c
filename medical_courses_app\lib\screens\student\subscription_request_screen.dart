import 'package:flutter/material.dart';
import '../../utils/constants.dart';

/// شاشة طلب الاشتراك
class SubscriptionRequestScreen extends StatelessWidget {
  final String courseId;

  const SubscriptionRequestScreen({
    super.key,
    required this.courseId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('طلب الاشتراك'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.request_page_outlined,
              size: 64,
              color: AppColors.primaryBlue,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'هذه شاشة طلب الاشتراك',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.darkText,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              'معرف الكورس: $courseId',
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'سيتم عرض نموذج طلب الاشتراك هنا',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
