{"logs": [{"outputFile": "com.medicalcourses.app-mergeDebugResources-35:/values-en-rAU/values-en-rAU.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,451,555,658,774", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "146,248,347,446,550,653,769,870"}, "to": {"startLines": "4,5,6,7,8,9,10,24", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "281,377,479,578,677,781,884,2050", "endColumns": "95,101,98,98,103,102,115,100", "endOffsets": "372,474,573,672,776,879,995,2146"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1203ed00ac77ef6e80766f7044873004\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2694643560e27514fec2bb7aa58bb43a\\transformed\\browser-1.4.0\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,153,250,359", "endColumns": "97,96,108,98", "endOffsets": "148,245,354,453"}, "to": {"startLines": "11,21,22,23", "startColumns": "4,4,4,4", "startOffsets": "1000,1745,1842,1951", "endColumns": "97,96,108,98", "endOffsets": "1093,1837,1946,2045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8798035275722523399db6d5c2b9413f\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-en-rAU\\values-en-rAU.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,125,187,252,316,393,458,548,633", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "120,182,247,311,388,453,543,628,697"}, "to": {"startLines": "12,13,14,15,16,17,18,19,20", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "1098,1168,1230,1295,1359,1436,1501,1591,1676", "endColumns": "69,61,64,63,76,64,89,84,68", "endOffsets": "1163,1225,1290,1354,1431,1496,1586,1671,1740"}}]}]}