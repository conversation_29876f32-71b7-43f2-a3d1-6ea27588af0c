import 'package:flutter/material.dart';

/// ثوابت الألوان المستخدمة في التطبيق
class AppColors {
  // الألوان الأساسية
  static const Color primaryBlue = Color(0xFF2196F3);
  static const Color medicalGreen = Color(0xFF4CAF50);
  static const Color white = Color(0xFFFFFFFF);
  static const Color lightGray = Color(0xFFF5F5F5);
  static const Color darkGray = Color(0xFF757575);
  
  // ألوان النصوص
  static const Color darkText = Color(0xFF212121);
  static const Color lightText = Color(0xFF757575);
  
  // ألوان الوضع المظلم
  static const Color darkBackground = Color(0xFF121212);
  static const Color darkSurface = Color(0xFF1E1E1E);
  static const Color darkBlue = Color(0xFF1976D2);
  
  // ألوان الحالة
  static const Color errorRed = Color(0xFFE53E3E);
  static const Color successGreen = Color(0xFF38A169);
  static const Color warningOrange = Color(0xFFED8936);
  static const Color infoBlue = Color(0xFF3182CE);
  
  // ألوان إضافية
  static const Color accent = Color(0xFF03DAC6);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFF000000);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color onSurface = Color(0xFF000000);
  static const Color background = Color(0xFFFAFAFA);
  static const Color onBackground = Color(0xFF000000);
}

/// ثوابت النصوص والرسائل
class AppStrings {
  // رسائل عامة
  static const String appName = 'الدورات الطبية';
  static const String loading = 'جاري التحميل...';
  static const String error = 'حدث خطأ';
  static const String retry = 'إعادة المحاولة';
  static const String cancel = 'إلغاء';
  static const String confirm = 'تأكيد';
  static const String save = 'حفظ';
  static const String delete = 'حذف';
  static const String edit = 'تعديل';
  static const String add = 'إضافة';
  static const String search = 'بحث';
  
  // رسائل المصادقة
  static const String login = 'تسجيل الدخول';
  static const String logout = 'تسجيل الخروج';
  static const String register = 'إنشاء حساب';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String forgotPassword = 'نسيت كلمة المرور؟';
  
  // رسائل الدورات
  static const String courses = 'الدورات';
  static const String myCourses = 'دوراتي';
  static const String allCourses = 'جميع الدورات';
  static const String courseDetails = 'تفاصيل الدورة';
  static const String enrollNow = 'سجل الآن';
  static const String startLearning = 'ابدأ التعلم';
}

/// ثوابت الأبعاد والمسافات
class AppDimensions {
  // المسافات الأساسية
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  
  // نصف أقطار الحواف
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  
  // أحجام الأيقونات
  static const double iconS = 16.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  
  // أحجام الخطوط
  static const double fontS = 12.0;
  static const double fontM = 14.0;
  static const double fontL = 16.0;
  static const double fontXL = 18.0;
  static const double fontXXL = 24.0;
  
  // ارتفاعات العناصر
  static const double buttonHeight = 48.0;
  static const double inputHeight = 56.0;
  static const double appBarHeight = 56.0;
  static const double cardHeight = 120.0;
}

/// ثوابت التكوين
class AppConfig {
  // إعدادات Firebase (يجب تحديثها بعد إعداد Firebase)
  static const String firebaseProjectId = 'medical-courses-app';
  
  // إعدادات التطبيق
  static const int maxVideoQuality = 720;
  static const int sessionTimeoutMinutes = 30;
  static const int maxDownloadRetries = 3;
  
  // إعدادات الأمان
  static const bool enableScreenshotProtection = true;
  static const bool enableJailbreakDetection = true;
  static const bool enableDebugMode = false;
  
  // إعدادات التخزين المؤقت
  static const int cacheExpirationHours = 24;
  static const int maxCacheSize = 100; // بالميجابايت
}

/// مسارات الأصول
class AppAssets {
  // الصور
  static const String imagesPath = 'assets/images/';
  static const String iconsPath = 'assets/icons/';
  static const String fontsPath = 'assets/fonts/';
  
  // أيقونات التطبيق
  static const String appLogo = '${iconsPath}app_logo.png';
  static const String defaultAvatar = '${imagesPath}default_avatar.png';
  static const String placeholder = '${imagesPath}placeholder.png';
  
  // أيقونات الدورات
  static const String videoIcon = '${iconsPath}video.png';
  static const String pdfIcon = '${iconsPath}pdf.png';
  static const String quizIcon = '${iconsPath}quiz.png';
}
