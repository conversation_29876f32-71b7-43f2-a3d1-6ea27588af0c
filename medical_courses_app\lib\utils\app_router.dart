import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../screens/auth/loading_screen.dart';
import '../screens/auth/login_screen.dart';
import '../screens/auth/signup_screen.dart';
import '../screens/student/home_screen.dart';
import '../screens/student/profile_screen.dart';
import '../screens/student/subjects_screen.dart';
import '../screens/student/courses_screen.dart';
import '../screens/student/course_details_screen.dart';
import '../screens/student/lecture_screen.dart';
import '../screens/student/subscription_request_screen.dart';
import '../screens/admin/admin_dashboard_screen.dart';
import '../screens/admin/manage_years_screen.dart';
import '../screens/admin/manage_subjects_screen.dart';
import '../screens/admin/manage_courses_screen.dart';
import '../screens/admin/manage_lectures_screen.dart';
import '../screens/admin/manage_lecture_content_screen.dart';
import '../screens/admin/manage_subscription_requests_screen.dart';
import '../screens/admin/manage_students_screen.dart';
import '../providers/auth_provider.dart';

/// مزود التوجيه الرئيسي للتطبيق
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/',
    routes: [
      // الشاشة الرئيسية - شاشة التحميل
      GoRoute(
        path: '/',
        name: 'loading',
        builder: (context, state) => const LoadingScreen(),
      ),

      // مسارات المصادقة
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: '/signup',
        name: 'signup',
        builder: (context, state) => const SignUpScreen(),
      ),

      // مسارات الطلاب
      GoRoute(
        path: '/student',
        name: 'student_home',
        builder: (context, state) => const StudentHomeScreen(),
      ),
      GoRoute(
        path: '/profile',
        name: 'student_profile',
        builder: (context, state) => const ProfileScreen(),
      ),
      GoRoute(
        path: '/subjects',
        name: 'subjects',
        builder: (context, state) => const SubjectsScreen(),
      ),
      GoRoute(
        path: '/courses/:subjectId',
        name: 'courses',
        builder: (context, state) {
          final subjectId = state.pathParameters['subjectId']!;
          return CoursesScreen(subjectId: subjectId);
        },
      ),
      GoRoute(
        path: '/course_details/:courseId',
        name: 'course_details',
        builder: (context, state) {
          final courseId = state.pathParameters['courseId']!;
          return CourseDetailsScreen(courseId: courseId);
        },
      ),
      GoRoute(
        path: '/lecture/:courseId/:lectureId',
        name: 'lecture',
        builder: (context, state) {
          final courseId = state.pathParameters['courseId']!;
          final lectureId = state.pathParameters['lectureId']!;
          return LectureScreen(courseId: courseId, lectureId: lectureId);
        },
      ),
      GoRoute(
        path: '/subscription_request/:courseId',
        name: 'subscription_request',
        builder: (context, state) {
          final courseId = state.pathParameters['courseId']!;
          return SubscriptionRequestScreen(courseId: courseId);
        },
      ),

      // مسارات الإدارة
      GoRoute(
        path: '/admin',
        name: 'admin_dashboard',
        builder: (context, state) => const AdminDashboardScreen(),
      ),
      GoRoute(
        path: '/admin/years',
        name: 'admin_years',
        builder: (context, state) => const ManageYearsScreen(),
      ),
      GoRoute(
        path: '/admin/subjects/:yearId',
        name: 'admin_subjects',
        builder: (context, state) {
          final yearId = state.pathParameters['yearId']!;
          return ManageSubjectsScreen(yearId: yearId);
        },
      ),
      GoRoute(
        path: '/admin/courses/:subjectId',
        name: 'admin_courses',
        builder: (context, state) {
          final subjectId = state.pathParameters['subjectId']!;
          return ManageCoursesScreen(subjectId: subjectId);
        },
      ),
      GoRoute(
        path: '/admin/lectures/:courseId',
        name: 'admin_lectures',
        builder: (context, state) {
          final courseId = state.pathParameters['courseId']!;
          return ManageLecturesScreen(courseId: courseId);
        },
      ),
      GoRoute(
        path: '/admin/lecture_content/:lectureId',
        name: 'admin_lecture_content',
        builder: (context, state) {
          final lectureId = state.pathParameters['lectureId']!;
          return ManageLectureContentScreen(lectureId: lectureId);
        },
      ),
      GoRoute(
        path: '/admin/subscription_requests',
        name: 'admin_subscription_requests',
        builder: (context, state) => const ManageSubscriptionRequestsScreen(),
      ),
      GoRoute(
        path: '/admin/students',
        name: 'admin_students',
        builder: (context, state) => const ManageStudentsScreen(),
      ),
    ],

    // معالج الأخطاء
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('خطأ في التنقل')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'الصفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.uri}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),

    // إعادة التوجيه بناءً على حالة المصادقة
    redirect: (context, state) {
      // تجنب الوصول لـ authProvider إذا لم يتم تهيئة Firebase بعد
      try {
        final authState = ref.read(authProvider);
        final currentPath = state.uri.path;
        final isLoggedIn = authState.isAuthenticated;
        final isLoggingIn = currentPath == '/login';
        final isSigningUp = currentPath == '/signup';
        final isPublicPath = isLoggingIn || isSigningUp || currentPath == '/';

        // إذا كانت الحالة تحميلية، ابق في الشاشة الحالية
        if (authState.isLoading) {
          return null; // Stay on current path (e.g., loading screen)
        }

        // إذا لم يكن مسجل الدخول ويحاول الوصول لمسار غير عام، وجهه لتسجيل الدخول
        if (!isLoggedIn && !isPublicPath) {
          return '/login';
        }

        // إذا كان مسجل الدخول ويحاول الوصول لمسار عام (تسجيل الدخول/التسجيل/التحميل)، وجهه لدوره
        if (isLoggedIn && isPublicPath) {
          return authState.user!.isAdmin ? '/admin' : '/student';
        }

        // لا توجيه
        return null;
      } catch (e) {
        // في حالة عدم تهيئة Firebase، البقاء في الصفحة الحالية
        return null;
      }
    },
  );
});

/// كلاس مساعد لإدارة المسارات
class AppRoutes {
  // مسارات المصادقة
  static const String loading = '/';
  static const String login = '/login';
  static const String signup = '/signup';

  // مسارات الطلاب
  static const String studentHome = '/student';
  static const String profile = '/profile';
  static const String subjects = '/subjects';
  static const String courses = '/courses';
  static const String courseDetails = '/course_details';
  static const String lecture = '/lecture';
  static const String subscriptionRequest = '/subscription_request';

  // مسارات الإدارة
  static const String adminDashboard = '/admin';
  static const String adminYears = '/admin/years';
  static const String adminSubjects = '/admin/subjects';
  static const String adminCourses = '/admin/courses';
  static const String adminLectures = '/admin/lectures';
  static const String adminLectureContent = '/admin/lecture_content';
  static const String adminSubscriptionRequests =
      '/admin/subscription_requests';
  static const String adminStudents = '/admin/students';
}

/// امتداد مساعد للتنقل
extension GoRouterExtension on GoRouter {
  /// الانتقال إلى شاشة تفاصيل الدورة
  void goToCourseDetails(String courseId) {
    go('${AppRoutes.courseDetails}/$courseId');
  }

  /// الانتقال إلى شاشة الكورسات
  void goToCourses(String subjectId) {
    go('${AppRoutes.courses}/$subjectId');
  }

  /// الانتقال إلى المحاضرة
  void goToLecture(String courseId, String lectureId) {
    go('${AppRoutes.lecture}/$courseId/$lectureId');
  }

  /// الانتقال إلى طلب الاشتراك
  void goToSubscriptionRequest(String courseId) {
    go('${AppRoutes.subscriptionRequest}/$courseId');
  }
}
