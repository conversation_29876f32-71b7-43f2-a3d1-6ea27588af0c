import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../screens/auth/loading_screen.dart';

/// مزود التوجيه الرئيسي للتطبيق
final routerProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/',
    routes: [
      // الشاشة الرئيسية - شاشة التحميل
      GoRoute(
        path: '/',
        name: 'loading',
        builder: (context, state) => const LoadingScreen(),
      ),
      
      // TODO: إضافة المسارات الأخرى هنا لاحقاً
      // مسارات المصادقة
      // GoRoute(
      //   path: '/login',
      //   name: 'login',
      //   builder: (context, state) => const LoginScreen(),
      // ),
      // GoRoute(
      //   path: '/register',
      //   name: 'register',
      //   builder: (context, state) => const RegisterScreen(),
      // ),
      
      // مسارات الطلاب
      // GoRoute(
      //   path: '/student',
      //   name: 'student_home',
      //   builder: (context, state) => const StudentHomeScreen(),
      //   routes: [
      //     GoRoute(
      //       path: '/courses',
      //       name: 'student_courses',
      //       builder: (context, state) => const StudentCoursesScreen(),
      //     ),
      //     GoRoute(
      //       path: '/course/:courseId',
      //       name: 'course_details',
      //       builder: (context, state) {
      //         final courseId = state.pathParameters['courseId']!;
      //         return CourseDetailsScreen(courseId: courseId);
      //       },
      //     ),
      //   ],
      // ),
      
      // مسارات الإدارة
      // GoRoute(
      //   path: '/admin',
      //   name: 'admin_home',
      //   builder: (context, state) => const AdminHomeScreen(),
      //   routes: [
      //     GoRoute(
      //       path: '/courses',
      //       name: 'admin_courses',
      //       builder: (context, state) => const AdminCoursesScreen(),
      //     ),
      //     GoRoute(
      //       path: '/users',
      //       name: 'admin_users',
      //       builder: (context, state) => const AdminUsersScreen(),
      //     ),
      //   ],
      // ),
    ],
    
    // معالج الأخطاء
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(
        title: const Text('خطأ في التنقل'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'الصفحة غير موجودة',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.uri}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/'),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
    
    // إعادة التوجيه (سيتم تطويرها لاحقاً)
    redirect: (context, state) {
      // TODO: إضافة منطق إعادة التوجيه بناءً على حالة المصادقة
      // مثال:
      // final isAuthenticated = ref.read(authProvider).isAuthenticated;
      // final isOnAuthPage = state.uri.path.startsWith('/auth');
      // 
      // if (!isAuthenticated && !isOnAuthPage) {
      //   return '/login';
      // }
      // 
      // if (isAuthenticated && isOnAuthPage) {
      //   return '/student';
      // }
      
      return null; // لا توجد إعادة توجيه حالياً
    },
  );
});

/// كلاس مساعد لإدارة المسارات
class AppRoutes {
  // مسارات المصادقة
  static const String loading = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  
  // مسارات الطلاب
  static const String studentHome = '/student';
  static const String studentCourses = '/student/courses';
  static const String courseDetails = '/student/course';
  static const String videoPlayer = '/student/video';
  static const String pdfViewer = '/student/pdf';
  static const String quiz = '/student/quiz';
  
  // مسارات الإدارة
  static const String adminHome = '/admin';
  static const String adminCourses = '/admin/courses';
  static const String adminUsers = '/admin/users';
  static const String adminSettings = '/admin/settings';
  
  // مسارات الملف الشخصي
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String help = '/help';
  static const String about = '/about';
}

/// امتداد مساعد للتنقل
extension GoRouterExtension on GoRouter {
  /// الانتقال إلى شاشة تفاصيل الدورة
  void goToCourseDetails(String courseId) {
    go('${AppRoutes.courseDetails}/$courseId');
  }
  
  /// الانتقال إلى مشغل الفيديو
  void goToVideoPlayer(String videoId, {String? courseId}) {
    final uri = Uri(
      path: AppRoutes.videoPlayer,
      queryParameters: {
        'videoId': videoId,
        if (courseId != null) 'courseId': courseId,
      },
    );
    go(uri.toString());
  }
  
  /// الانتقال إلى عارض PDF
  void goToPdfViewer(String pdfUrl, {String? title}) {
    final uri = Uri(
      path: AppRoutes.pdfViewer,
      queryParameters: {
        'url': pdfUrl,
        if (title != null) 'title': title,
      },
    );
    go(uri.toString());
  }
  
  /// الانتقال إلى الاختبار
  void goToQuiz(String quizId, {String? courseId}) {
    final uri = Uri(
      path: AppRoutes.quiz,
      queryParameters: {
        'quizId': quizId,
        if (courseId != null) 'courseId': courseId,
      },
    );
    go(uri.toString());
  }
}
