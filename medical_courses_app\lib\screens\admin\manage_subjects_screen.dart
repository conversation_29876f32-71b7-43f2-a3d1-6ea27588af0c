import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../utils/constants.dart';
import '../../providers/admin_provider.dart';
import '../../models/subject.dart';
import '../../widgets/admin/subject_form_dialog.dart';

/// شاشة إدارة المواد ضمن سنة
class ManageSubjectsScreen extends ConsumerWidget {
  final String yearId;

  const ManageSubjectsScreen({
    super.key,
    required this.yearId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subjectsAsync = ref.watch(subjectsProvider(yearId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المواد'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showSubjectDialog(context, ref),
          ),
        ],
      ),
      body: subjectsAsync.when(
        data: (subjects) => _buildSubjectsList(context, ref, subjects),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.errorRed,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Text(
                'خطأ في تحميل المواد',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              ElevatedButton(
                onPressed: () => ref.refresh(subjectsProvider(yearId)),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showSubjectDialog(context, ref),
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.add, color: AppColors.white),
      ),
    );
  }

  Widget _buildSubjectsList(
      BuildContext context, WidgetRef ref, List<Subject> subjects) {
    if (subjects.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.subject,
              size: 64,
              color: AppColors.lightText,
            ),
            SizedBox(height: AppDimensions.paddingM),
            Text(
              'لا توجد مواد دراسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.lightText,
              ),
            ),
            SizedBox(height: AppDimensions.paddingS),
            Text(
              'اضغط على زر + لإضافة مادة جديدة',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppDimensions.paddingM,
        mainAxisSpacing: AppDimensions.paddingM,
        childAspectRatio: 0.8,
      ),
      itemCount: subjects.length,
      itemBuilder: (context, index) {
        final subject = subjects[index];
        return _buildSubjectCard(context, ref, subject);
      },
    );
  }

  Widget _buildSubjectCard(
      BuildContext context, WidgetRef ref, Subject subject) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => context.go('/admin/courses/${subject.id}'),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المادة
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(AppDimensions.radiusM),
                  ),
                  color: AppColors.lightGray.withValues(alpha: 0.3),
                ),
                child: subject.imageUrl != null
                    ? ClipRRect(
                        borderRadius: const BorderRadius.vertical(
                          top: Radius.circular(AppDimensions.radiusM),
                        ),
                        child: Image.network(
                          subject.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.subject,
                              size: 40,
                              color: AppColors.lightText,
                            );
                          },
                        ),
                      )
                    : const Icon(
                        Icons.subject,
                        size: 40,
                        color: AppColors.lightText,
                      ),
              ),
            ),
            // معلومات المادة
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingS),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subject.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (subject.description != null) ...[
                      const SizedBox(height: AppDimensions.paddingXS),
                      Text(
                        subject.description!,
                        style: const TextStyle(
                          fontSize: 12,
                          color: AppColors.lightText,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'ترتيب: ${subject.order ?? 'غير محدد'}',
                          style: const TextStyle(
                            fontSize: 10,
                            color: AppColors.lightText,
                          ),
                        ),
                        PopupMenuButton<String>(
                          onSelected: (value) {
                            switch (value) {
                              case 'edit':
                                _showSubjectDialog(context, ref,
                                    subject: subject);
                                break;
                              case 'courses':
                                context.go('/admin/courses/${subject.id}');
                                break;
                              case 'delete':
                                _showDeleteDialog(context, ref, subject);
                                break;
                            }
                          },
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'edit',
                              child: ListTile(
                                leading: Icon(Icons.edit),
                                title: Text('تعديل'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'courses',
                              child: ListTile(
                                leading: Icon(Icons.video_library),
                                title: Text('إدارة الكورسات'),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: ListTile(
                                leading: Icon(Icons.delete,
                                    color: AppColors.errorRed),
                                title: Text('حذف',
                                    style:
                                        TextStyle(color: AppColors.errorRed)),
                                contentPadding: EdgeInsets.zero,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSubjectDialog(BuildContext context, WidgetRef ref,
      {Subject? subject}) {
    showDialog(
      context: context,
      builder: (context) => SubjectFormDialog(
        subject: subject,
        yearId: yearId,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Subject subject) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
            'هل أنت متأكد من حذف المادة "${subject.title}"؟\nسيتم حذف جميع الكورسات والمحاضرات المرتبطة بها.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref
                  .read(subjectManagementProvider.notifier)
                  .deleteSubject(subject.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }
}
