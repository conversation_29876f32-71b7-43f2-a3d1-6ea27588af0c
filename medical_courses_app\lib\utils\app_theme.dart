import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'constants.dart';

/// كلاس إدارة ثيمات التطبيق
class AppTheme {
  /// الثيم الفاتح
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,

    // نظام الألوان
    colorScheme: const ColorScheme.light(
      primary: AppColors.primaryBlue,
      secondary: AppColors.medicalGreen,
      surface: AppColors.white,
      error: AppColors.errorRed,
      onPrimary: AppColors.onPrimary,
      onSecondary: AppColors.onSecondary,
      onSurface: AppColors.onSurface,
      onError: AppColors.white,
    ),

    // شريط التطبيق
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.primaryBlue,
      foregroundColor: AppColors.white,
      elevation: 0,
      centerTitle: true,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    ),

    // الأزرار المرفوعة
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        minimumSize: const Size(double.infinity, AppDimensions.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        elevation: 2,
      ),
    ),

    // الأزرار المحددة
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColors.primaryBlue,
        minimumSize: const Size(double.infinity, AppDimensions.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        side: const BorderSide(color: AppColors.primaryBlue),
      ),
    ),

    // الأزرار النصية
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColors.primaryBlue,
        minimumSize: const Size(0, AppDimensions.buttonHeight),
      ),
    ),

    // حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.lightGray,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        borderSide: const BorderSide(color: AppColors.primaryBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        borderSide: const BorderSide(color: AppColors.errorRed, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingM,
      ),
    ),

    // البطاقات - TODO: إضافة تخصيص البطاقات لاحقاً
    // cardTheme: CardTheme(
    //   elevation: 2,
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(AppDimensions.radiusL),
    //   ),
    //   margin: const EdgeInsets.all(AppDimensions.paddingS),
    // ),

    // النصوص
    textTheme: const TextTheme(
      displayLarge: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic', // TODO: إضافة الخط العربي لاحقاً
      ),
      displayMedium: TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      displaySmall: TextStyle(
        fontSize: AppDimensions.fontXXL,
        fontWeight: FontWeight.bold,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      headlineLarge: TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.w600,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      headlineMedium: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w600,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      headlineSmall: TextStyle(
        fontSize: AppDimensions.fontXL,
        fontWeight: FontWeight.w600,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      titleLarge: TextStyle(
        fontSize: AppDimensions.fontL,
        fontWeight: FontWeight.w500,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      titleMedium: TextStyle(
        fontSize: AppDimensions.fontM,
        fontWeight: FontWeight.w500,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      titleSmall: TextStyle(
        fontSize: AppDimensions.fontS,
        fontWeight: FontWeight.w500,
        color: AppColors.lightText,
        // fontFamily: 'NotoSansArabic',
      ),
      bodyLarge: TextStyle(
        fontSize: AppDimensions.fontL,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      bodyMedium: TextStyle(
        fontSize: AppDimensions.fontM,
        color: AppColors.darkText,
        // fontFamily: 'NotoSansArabic',
      ),
      bodySmall: TextStyle(
        fontSize: AppDimensions.fontS,
        color: AppColors.lightText,
        // fontFamily: 'NotoSansArabic',
      ),
    ),
  );

  /// الثيم المظلم
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    brightness: Brightness.dark,

    // نظام الألوان
    colorScheme: const ColorScheme.dark(
      primary: AppColors.darkBlue,
      secondary: AppColors.medicalGreen,
      surface: AppColors.darkSurface,
      error: AppColors.errorRed,
      onPrimary: AppColors.white,
      onSecondary: AppColors.white,
      onSurface: AppColors.white,
      onError: AppColors.white,
    ),

    // شريط التطبيق
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.darkSurface,
      foregroundColor: AppColors.white,
      elevation: 0,
      centerTitle: true,
      systemOverlayStyle: SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    ),

    // الأزرار المرفوعة
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.darkBlue,
        foregroundColor: AppColors.white,
        minimumSize: const Size(double.infinity, AppDimensions.buttonHeight),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        ),
        elevation: 2,
      ),
    ),

    // حقول الإدخال
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: AppColors.darkSurface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        borderSide: BorderSide.none,
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        borderSide: BorderSide.none,
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        borderSide: const BorderSide(color: AppColors.darkBlue, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        borderSide: const BorderSide(color: AppColors.errorRed, width: 2),
      ),
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingM,
        vertical: AppDimensions.paddingM,
      ),
    ),

    // البطاقات - TODO: إضافة تخصيص البطاقات لاحقاً
    // cardTheme: CardTheme(
    //   elevation: 2,
    //   color: AppColors.darkSurface,
    //   shape: RoundedRectangleBorder(
    //     borderRadius: BorderRadius.circular(AppDimensions.radiusL),
    //   ),
    //   margin: const EdgeInsets.all(AppDimensions.paddingS),
    // ),
  );
}
