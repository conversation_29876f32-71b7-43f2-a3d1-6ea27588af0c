import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../utils/constants.dart';
import '../../models/lecture_content.dart';
import '../../providers/admin_provider.dart';

/// نموذج إضافة/تعديل البطاقة التعليمية
class FlashcardFormDialog extends ConsumerStatefulWidget {
  final Flashcard? flashcard;
  final int? index;

  const FlashcardFormDialog({
    super.key,
    this.flashcard,
    this.index,
  });

  @override
  ConsumerState<FlashcardFormDialog> createState() =>
      _FlashcardFormDialogState();
}

class _FlashcardFormDialogState extends ConsumerState<FlashcardFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _questionController = TextEditingController();
  final _answerController = TextEditingController();

  bool _isImageType = false;
  PlatformFile? _selectedImage;
  String? _currentImageUrl;
  bool _isUploading = false;

  bool get isEditing => widget.flashcard != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _questionController.text = widget.flashcard!.question;
      _answerController.text = widget.flashcard!.answer;
      // تحديد نوع البطاقة بناءً على المحتوى
      _isImageType = widget.flashcard!.question.startsWith('http') ||
          widget.flashcard!.answer.startsWith('http');
      if (_isImageType) {
        _currentImageUrl = widget.flashcard!.question.startsWith('http')
            ? widget.flashcard!.question
            : widget.flashcard!.answer;
      }
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    _answerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
          isEditing ? 'تعديل البطاقة التعليمية' : 'إضافة بطاقة تعليمية جديدة'),
      content: SizedBox(
        width: double.maxFinite,
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // اختيار نوع البطاقة
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('نوع البطاقة:'),
                    const SizedBox(height: AppDimensions.paddingS),
                    SizedBox(
                      width: double.infinity,
                      child: SegmentedButton<bool>(
                        segments: const [
                          ButtonSegment<bool>(
                            value: false,
                            label: Text('نص'),
                            icon: Icon(Icons.text_fields),
                          ),
                          ButtonSegment<bool>(
                            value: true,
                            label: Text('صورة'),
                            icon: Icon(Icons.image),
                          ),
                        ],
                        selected: {_isImageType},
                        onSelectionChanged: (Set<bool> newSelection) {
                          setState(() {
                            _isImageType = newSelection.first;
                            // مسح البيانات عند تغيير النوع
                            _questionController.clear();
                            _answerController.clear();
                            _selectedImage = null;
                            _currentImageUrl = null;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppDimensions.paddingM),

                if (_isImageType) ...[
                  // قسم الصورة
                  _buildImageSection(),
                ] else ...[
                  // قسم النص
                  _buildTextSection(),
                ],
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isUploading ? null : _saveFlashcard,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
          ),
          child: _isUploading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                  ),
                )
              : Text(
                  isEditing ? 'تحديث' : 'إضافة',
                  style: const TextStyle(color: AppColors.white),
                ),
        ),
      ],
    );
  }

  Widget _buildTextSection() {
    return Column(
      children: [
        TextFormField(
          controller: _questionController,
          decoration: const InputDecoration(
            labelText: 'السؤال *',
            hintText: 'اكتب السؤال هنا',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال السؤال';
            }
            return null;
          },
        ),
        const SizedBox(height: AppDimensions.paddingM),
        TextFormField(
          controller: _answerController,
          decoration: const InputDecoration(
            labelText: 'الإجابة *',
            hintText: 'اكتب الإجابة هنا',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال الإجابة';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _questionController,
          decoration: const InputDecoration(
            labelText: 'عنوان البطاقة *',
            hintText: 'عنوان أو وصف للصورة',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال عنوان البطاقة';
            }
            return null;
          },
        ),
        const SizedBox(height: AppDimensions.paddingM),
        const Text(
          'صورة البطاقة *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        if (_currentImageUrl != null || _selectedImage != null)
          Container(
            height: 150,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.lightGray),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: _selectedImage != null
                ? Image.memory(
                    _selectedImage!.bytes!,
                    fit: BoxFit.cover,
                  )
                : _currentImageUrl != null
                    ? Image.network(
                        _currentImageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(Icons.error);
                        },
                      )
                    : const Icon(Icons.image),
          ),
        const SizedBox(height: AppDimensions.paddingS),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _pickImage,
                icon: const Icon(Icons.image),
                label: const Text('اختيار صورة'),
              ),
            ),
            if (_selectedImage != null || _currentImageUrl != null) ...[
              const SizedBox(width: AppDimensions.paddingS),
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedImage = null;
                    _currentImageUrl = null;
                  });
                },
                icon: const Icon(Icons.delete, color: AppColors.errorRed),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppDimensions.paddingM),
        TextFormField(
          controller: _answerController,
          decoration: const InputDecoration(
            labelText: 'وصف الصورة (اختياري)',
            hintText: 'وصف أو تفسير للصورة',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // التحقق من حجم الملف (أقل من 5 ميجا)
        if (file.size > 5 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة يجب أن يكون أقل من 5 ميجابايت'),
                backgroundColor: AppColors.errorRed,
              ),
            );
          }
          return;
        }

        setState(() {
          _selectedImage = file;
          _currentImageUrl = null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }

  Future<void> _saveFlashcard() async {
    if (!_formKey.currentState!.validate()) return;

    if (_isImageType && _selectedImage == null && _currentImageUrl == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار صورة للبطاقة'),
          backgroundColor: AppColors.errorRed,
        ),
      );
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      String question = _questionController.text.trim();
      String answer = _answerController.text.trim();

      // رفع الصورة إذا تم اختيار صورة جديدة
      if (_isImageType && _selectedImage != null) {
        final imageId = DateTime.now().millisecondsSinceEpoch.toString();
        final imageUrl = await ref
            .read(lectureContentManagementProvider.notifier)
            .uploadImage(_selectedImage!, 'flashcards', imageId);

        // حفظ رابط الصورة في السؤال
        question = imageUrl;
      } else if (_isImageType && _currentImageUrl != null) {
        question = _currentImageUrl!;
      }

      final flashcard = Flashcard(
        question: question,
        answer: answer,
      );

      if (mounted) {
        Navigator.of(context).pop(flashcard);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }
}
