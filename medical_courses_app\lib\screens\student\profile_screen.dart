import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../providers/auth_provider.dart';
import '../../utils/constants.dart';

/// شاشة الملف الشخصي للطالب
class ProfileScreen extends ConsumerStatefulWidget {
  const ProfileScreen({super.key});

  @override
  ConsumerState<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends ConsumerState<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  /// تحميل بيانات المستخدم
  void _loadUserData() {
    final user = ref.read(authProvider).user;
    if (user != null) {
      _nameController.text = user.name;
      _emailController.text = user.email;
    }
  }

  /// حفظ التغييرات
  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    final user = ref.read(authProvider).user;
    if (user == null) return;

    final updatedUser = user.copyWith(name: _nameController.text.trim());

    await ref.read(authProvider.notifier).updateUser(updatedUser);

    setState(() {
      _isEditing = false;
    });

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ التغييرات بنجاح'),
          backgroundColor: AppColors.successGreen,
        ),
      );
    }
  }

  /// إلغاء التعديل
  void _cancelEdit() {
    _loadUserData();
    setState(() {
      _isEditing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final user = authState.user;

    if (user == null) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.go('/student'),
        ),
        actions: [
          if (_isEditing) ...[
            TextButton(onPressed: _cancelEdit, child: const Text('إلغاء')),
            TextButton(
              onPressed: authState.isLoading ? null : _saveChanges,
              child: authState.isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('حفظ'),
            ),
          ] else
            IconButton(
              icon: const Icon(Icons.edit_outlined),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              const SizedBox(height: AppDimensions.paddingL),

              // صورة الملف الشخصي
              Stack(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundColor: AppColors.primaryBlue.withValues(
                      alpha: 0.1,
                    ),
                    backgroundImage: user.profileImageUrl != null
                        ? NetworkImage(user.profileImageUrl!)
                        : null,
                    child: user.profileImageUrl == null
                        ? Icon(
                            Icons.person,
                            size: 60,
                            color: AppColors.primaryBlue,
                          )
                        : null,
                  ),
                  if (_isEditing)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue,
                          shape: BoxShape.circle,
                          border: Border.all(color: AppColors.white, width: 2),
                        ),
                        child: IconButton(
                          icon: const Icon(
                            Icons.camera_alt,
                            color: AppColors.white,
                            size: 20,
                          ),
                          onPressed: () {
                            // TODO: إضافة تغيير صورة الملف الشخصي
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('سيتم إضافة هذه الميزة قريباً'),
                                backgroundColor: AppColors.infoBlue,
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: AppDimensions.paddingXL),

              // معلومات المستخدم
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingL),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المعلومات الشخصية',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(height: AppDimensions.paddingL),

                      // حقل الاسم
                      TextFormField(
                        controller: _nameController,
                        enabled: _isEditing,
                        decoration: const InputDecoration(
                          labelText: 'الاسم الكامل',
                          prefixIcon: Icon(Icons.person_outlined),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'يرجى إدخال الاسم';
                          }
                          if (value.trim().length < 2) {
                            return 'الاسم يجب أن يكون حرفين على الأقل';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: AppDimensions.paddingM),

                      // حقل البريد الإلكتروني (للقراءة فقط)
                      TextFormField(
                        controller: _emailController,
                        enabled: false,
                        decoration: const InputDecoration(
                          labelText: 'البريد الإلكتروني',
                          prefixIcon: Icon(Icons.email_outlined),
                          helperText: 'لا يمكن تغيير البريد الإلكتروني',
                        ),
                      ),

                      const SizedBox(height: AppDimensions.paddingM),

                      // دور المستخدم (للقراءة فقط)
                      TextFormField(
                        initialValue: user.isStudent ? 'طالب' : 'مشرف',
                        enabled: false,
                        decoration: const InputDecoration(
                          labelText: 'نوع الحساب',
                          prefixIcon: Icon(Icons.badge_outlined),
                        ),
                      ),

                      const SizedBox(height: AppDimensions.paddingM),

                      // تاريخ الانضمام (للقراءة فقط)
                      TextFormField(
                        initialValue: _formatDate(user.createdAt),
                        enabled: false,
                        decoration: const InputDecoration(
                          labelText: 'تاريخ الانضمام',
                          prefixIcon: Icon(Icons.calendar_today_outlined),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: AppDimensions.paddingL),

              // إعدادات الحساب
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(AppDimensions.paddingL),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إعدادات الحساب',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),

                      const SizedBox(height: AppDimensions.paddingM),

                      ListTile(
                        leading: const Icon(Icons.lock_outlined),
                        title: const Text('تغيير كلمة المرور'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          // TODO: إضافة شاشة تغيير كلمة المرور
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('سيتم إضافة هذه الميزة قريباً'),
                              backgroundColor: AppColors.infoBlue,
                            ),
                          );
                        },
                      ),

                      const Divider(),

                      ListTile(
                        leading: const Icon(Icons.notifications_outlined),
                        title: const Text('إعدادات الإشعارات'),
                        trailing: const Icon(Icons.arrow_forward_ios),
                        onTap: () {
                          // TODO: إضافة شاشة إعدادات الإشعارات
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('سيتم إضافة هذه الميزة قريباً'),
                              backgroundColor: AppColors.infoBlue,
                            ),
                          );
                        },
                      ),

                      const Divider(),

                      ListTile(
                        leading: const Icon(
                          Icons.logout,
                          color: AppColors.errorRed,
                        ),
                        title: const Text(
                          'تسجيل الخروج',
                          style: TextStyle(color: AppColors.errorRed),
                        ),
                        onTap: () async {
                          final shouldLogout = await showDialog<bool>(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: const Text('تسجيل الخروج'),
                              content: const Text(
                                'هل أنت متأكد من رغبتك في تسجيل الخروج؟',
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(false),
                                  child: const Text('إلغاء'),
                                ),
                                TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(true),
                                  child: const Text(
                                    'تسجيل الخروج',
                                    style: TextStyle(color: AppColors.errorRed),
                                  ),
                                ),
                              ],
                            ),
                          );

                          if (shouldLogout == true) {
                            await ref.read(authProvider.notifier).signOut();
                            if (mounted) {
                              context.go(
                                '/login',
                              ); // الانتقال الصريح إلى شاشة تسجيل الدخول
                            }
                          }
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
