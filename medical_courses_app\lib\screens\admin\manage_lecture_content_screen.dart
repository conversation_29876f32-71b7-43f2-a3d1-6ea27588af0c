import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import 'package:go_router/go_router.dart';
import '../../utils/constants.dart';
import '../../providers/admin_provider.dart';
import '../../providers/auth_provider.dart';
import '../../models/lecture_content.dart';
import '../../widgets/admin/flashcard_form_dialog.dart';
import '../../widgets/admin/mcq_form_dialog.dart';

/// شاشة إدارة محتوى المحاضرة
class ManageLectureContentScreen extends ConsumerStatefulWidget {
  final String lectureId;

  const ManageLectureContentScreen({super.key, required this.lectureId});

  @override
  ConsumerState<ManageLectureContentScreen> createState() =>
      _ManageLectureContentScreenState();
}

class _ManageLectureContentScreenState
    extends ConsumerState<ManageLectureContentScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isUploading = false;
  String? _uploadingType;

  // قوائم محلية للبطاقات والأسئلة
  List<Flashcard> _localFlashcards = [];
  List<QuizQuestion> _localQuizQuestions = [];
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final contentAsync = ref.watch(lectureContentProvider(widget.lectureId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة محتوى المحاضرة'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            print('DEBUG: Back button pressed on ${runtimeType.toString()}.');
            if (context.canPop()) {
              context.pop();
              print('DEBUG: context.pop() called.');
            } else {
              final authState = ref.read(authProvider);
              if (authState.isAuthenticated) {
                final user = authState.user;
                if (user != null) {
                  context.go(user.isAdmin ? '/admin' : '/student');
                  print(
                      'DEBUG: No screen to pop, redirecting to home based on role.');
                }
              } else {
                context.go('/login');
                print('DEBUG: No screen to pop, redirecting to login.');
              }
            }
          },
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withValues(alpha: 0.7),
          indicatorColor: AppColors.white,
          tabs: const [
            Tab(icon: Icon(Icons.play_circle), text: 'فيديو'),
            Tab(icon: Icon(Icons.description), text: 'ملخص'),
            Tab(icon: Icon(Icons.quiz), text: 'بطاقات'),
            Tab(icon: Icon(Icons.assignment), text: 'اختبار'),
          ],
        ),
      ),
      body: contentAsync.when(
        data: (content) {
          // تحديث القوائم المحلية عند تحميل البيانات
          if (!_hasUnsavedChanges) {
            _localFlashcards = List.from(content?.flashcards ?? []);
            _localQuizQuestions = List.from(content?.quiz ?? []);
          }

          return _buildContentManagement(content);
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => _buildErrorWidget(error),
      ),
      floatingActionButton: _hasUnsavedChanges
          ? FloatingActionButton.extended(
              onPressed: _saveAllChanges,
              backgroundColor: AppColors.medicalGreen,
              icon: const Icon(Icons.save, color: AppColors.white),
              label: const Text(
                'حفظ التغييرات',
                style: TextStyle(color: AppColors.white),
              ),
            )
          : null,
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: AppColors.errorRed),
          const SizedBox(height: AppDimensions.paddingM),
          Text(
            'خطأ في تحميل المحتوى',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppDimensions.paddingS),
          Text(
            error.toString(),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppDimensions.paddingM),
          ElevatedButton(
            onPressed: () =>
                ref.refresh(lectureContentProvider(widget.lectureId)),
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildContentManagement(LectureContent? content) {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildVideoTab(content),
        _buildSummaryTab(content),
        _buildFlashcardsTab(content),
        _buildQuizTab(content),
      ],
    );
  }

  Widget _buildVideoTab(LectureContent? content) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'فيديو المحاضرة',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          if (content?.videoUrl != null) ...[
            _buildVideoUploadedCard(content!.videoUrl!),
          ] else ...[
            _buildVideoUploadCard(),
          ],
        ],
      ),
    );
  }

  Widget _buildVideoUploadedCard(String videoUrl) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            const Icon(
              Icons.check_circle,
              color: AppColors.medicalGreen,
              size: 48,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'تم رفع الفيديو بنجاح',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.medicalGreen,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              'رابط الفيديو: ${videoUrl.length > 50 ? videoUrl.substring(0, 50) : videoUrl}...',
              style: const TextStyle(fontSize: 12, color: AppColors.lightText),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _pickVideo(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('استبدال الفيديو'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: AppColors.white,
                  ),
                ),
                OutlinedButton.icon(
                  onPressed: () => _removeVideo(),
                  icon: const Icon(Icons.delete, color: AppColors.errorRed),
                  label: const Text(
                    'حذف الفيديو',
                    style: TextStyle(color: AppColors.errorRed),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoUploadCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            const Icon(Icons.video_call, size: 64, color: AppColors.lightText),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'لم يتم رفع فيديو بعد',
              style: TextStyle(fontSize: 18, color: AppColors.lightText),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'اختر ملف فيديو لرفعه للمحاضرة',
              style: TextStyle(fontSize: 14, color: AppColors.lightText),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            ElevatedButton.icon(
              onPressed: _isUploading ? null : () => _pickVideo(),
              icon: _isUploading && _uploadingType == 'video'
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.upload),
              label: Text(
                _isUploading && _uploadingType == 'video'
                    ? 'جاري الرفع...'
                    : 'رفع فيديو',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingL,
                  vertical: AppDimensions.paddingM,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryTab(LectureContent? content) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'ملخص المحاضرة',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: AppDimensions.paddingM),
          if (content?.summaryContent != null) ...[
            _buildSummaryUploadedCard(content!.summaryContent!),
          ] else ...[
            _buildSummaryUploadCard(),
          ],
        ],
      ),
    );
  }

  Widget _buildSummaryUploadedCard(String summaryUrl) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          children: [
            const Icon(
              Icons.check_circle,
              color: AppColors.medicalGreen,
              size: 48,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'تم رفع الملخص بنجاح',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.medicalGreen,
              ),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              'رابط الملخص: ${summaryUrl.length > 50 ? summaryUrl.substring(0, 50) : summaryUrl}...',
              style: const TextStyle(fontSize: 12, color: AppColors.lightText),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _pickSummary(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('استبدال الملخص'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: AppColors.white,
                  ),
                ),
                OutlinedButton.icon(
                  onPressed: () => _removeSummary(),
                  icon: const Icon(Icons.delete, color: AppColors.errorRed),
                  label: const Text(
                    'حذف الملخص',
                    style: TextStyle(color: AppColors.errorRed),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryUploadCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            const Icon(
              Icons.picture_as_pdf,
              size: 64,
              color: AppColors.lightText,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'لم يتم رفع ملخص بعد',
              style: TextStyle(fontSize: 18, color: AppColors.lightText),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'اختر ملف PDF لرفعه كملخص للمحاضرة',
              style: TextStyle(fontSize: 14, color: AppColors.lightText),
            ),
            const SizedBox(height: AppDimensions.paddingM),
            ElevatedButton.icon(
              onPressed: _isUploading ? null : () => _pickSummary(),
              icon: _isUploading && _uploadingType == 'summary'
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.upload),
              label: Text(
                _isUploading && _uploadingType == 'summary'
                    ? 'جاري الرفع...'
                    : 'رفع ملخص PDF',
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryBlue,
                foregroundColor: AppColors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: AppDimensions.paddingL,
                  vertical: AppDimensions.paddingM,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlashcardsTab(LectureContent? content) {
    return Column(
      children: [
        // Header with title and add button
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Row(
            children: [
              const Expanded(
                child: Text(
                  'البطاقات التعليمية',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: AppDimensions.paddingS),
              Flexible(
                child: ElevatedButton.icon(
                  onPressed: () => _showFlashcardDialog(),
                  icon: const Icon(Icons.add, size: 20),
                  label: const Text('إضافة بطاقة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: AppColors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Content area
        Expanded(
          child: _localFlashcards.isNotEmpty
              ? ListView.builder(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                  ),
                  itemCount: _localFlashcards.length,
                  itemBuilder: (context, index) {
                    final flashcard = _localFlashcards[index];
                    return _buildFlashcardItem(flashcard, index);
                  },
                )
              : Center(
                  child: Padding(
                    padding: const EdgeInsets.all(AppDimensions.paddingM),
                    child: _buildEmptyFlashcardsCard(),
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyFlashcardsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            const Icon(Icons.quiz, size: 64, color: AppColors.lightText),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'لا توجد بطاقات تعليمية',
              style: TextStyle(fontSize: 18, color: AppColors.lightText),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'اضغط على "إضافة بطاقة" لإنشاء بطاقة تعليمية جديدة',
              style: TextStyle(fontSize: 14, color: AppColors.lightText),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFlashcardItem(Flashcard flashcard, int index) {
    final isImageCard = flashcard.question.startsWith('http');

    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingM),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.primaryBlue,
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppDimensions.paddingM),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isImageCard ? 'بطاقة صورة' : 'بطاقة نص',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      if (!isImageCard) ...[
                        const SizedBox(height: AppDimensions.paddingS),
                        Text(
                          'السؤال: ${flashcard.question}',
                          style: const TextStyle(fontSize: 14),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: AppDimensions.paddingXS),
                        Text(
                          'الإجابة: ${flashcard.answer}',
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.lightText,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _editFlashcard(index);
                        break;
                      case 'delete':
                        _deleteFlashcard(index);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: ListTile(
                        leading: Icon(Icons.edit),
                        title: Text('تعديل'),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: ListTile(
                        leading: Icon(Icons.delete, color: AppColors.errorRed),
                        title: Text(
                          'حذف',
                          style: TextStyle(color: AppColors.errorRed),
                        ),
                        contentPadding: EdgeInsets.zero,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            if (isImageCard) ...[
              const SizedBox(height: AppDimensions.paddingM),
              Container(
                height: 150,
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  border: Border.all(color: AppColors.lightGray),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  child: Image.network(
                    flashcard.question,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Icon(Icons.error, color: AppColors.errorRed),
                      );
                    },
                  ),
                ),
              ),
              if (flashcard.answer.isNotEmpty) ...[
                const SizedBox(height: AppDimensions.paddingS),
                Text(
                  'الوصف: ${flashcard.answer}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.lightText,
                  ),
                ),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuizTab(LectureContent? content) {
    return Column(
      children: [
        // Header with title and add button
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Row(
            children: [
              const Expanded(
                child: Text(
                  'أسئلة الاختبار',
                  style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
                ),
              ),
              const SizedBox(width: AppDimensions.paddingS),
              Flexible(
                child: ElevatedButton.icon(
                  onPressed: () => _showQuizDialog(),
                  icon: const Icon(Icons.add, size: 20),
                  label: const Text('إضافة سؤال'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryBlue,
                    foregroundColor: AppColors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: AppDimensions.paddingM,
                      vertical: AppDimensions.paddingS,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Content area
        Expanded(
          child: _localQuizQuestions.isNotEmpty
              ? ListView.builder(
                  padding: const EdgeInsets.symmetric(
                    horizontal: AppDimensions.paddingM,
                  ),
                  itemCount: _localQuizQuestions.length,
                  itemBuilder: (context, index) {
                    final question = _localQuizQuestions[index];
                    return _buildQuizItem(question, index);
                  },
                )
              : Center(
                  child: Padding(
                    padding: const EdgeInsets.all(AppDimensions.paddingM),
                    child: _buildEmptyQuizCard(),
                  ),
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyQuizCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppDimensions.paddingL),
        child: Column(
          children: [
            const Icon(Icons.assignment, size: 64, color: AppColors.lightText),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'لا توجد أسئلة اختبار',
              style: TextStyle(fontSize: 18, color: AppColors.lightText),
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'اضغط على "إضافة سؤال" لإنشاء سؤال اختبار جديد',
              style: TextStyle(fontSize: 14, color: AppColors.lightText),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuizItem(QuizQuestion question, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      child: ExpansionTile(
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: AppColors.primaryBlue,
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.paddingM),
            Expanded(
              child: Text(
                question.question,
                style: const TextStyle(fontWeight: FontWeight.bold),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(AppDimensions.paddingM),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'الخيارات:',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                const SizedBox(height: AppDimensions.paddingS),
                ...question.options.asMap().entries.map((entry) {
                  final optionIndex = entry.key;
                  final option = entry.value;
                  final isCorrect = optionIndex == question.correctAnswer;
                  return Padding(
                    padding: const EdgeInsets.only(
                      bottom: AppDimensions.paddingXS,
                    ),
                    child: Row(
                      children: [
                        Icon(
                          isCorrect
                              ? Icons.check_circle
                              : Icons.radio_button_unchecked,
                          color: isCorrect
                              ? AppColors.medicalGreen
                              : AppColors.lightText,
                          size: 20,
                        ),
                        const SizedBox(width: AppDimensions.paddingS),
                        Expanded(
                          child: Text(
                            '${optionIndex + 1}. $option',
                            style: TextStyle(
                              fontWeight: isCorrect
                                  ? FontWeight.bold
                                  : FontWeight.normal,
                              color: isCorrect
                                  ? AppColors.medicalGreen
                                  : AppColors.darkText,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }),
                if (question.explanation != null) ...[
                  const SizedBox(height: AppDimensions.paddingM),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(AppDimensions.paddingM),
                    decoration: BoxDecoration(
                      color: AppColors.lightGray.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(
                        AppDimensions.radiusS,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'الشرح التوضيحي:',
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: AppDimensions.paddingS),
                        Text(question.explanation!),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: AppDimensions.paddingM),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: () => _editQuizQuestion(index),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                    const SizedBox(width: AppDimensions.paddingS),
                    TextButton.icon(
                      onPressed: () => _deleteQuizQuestion(index),
                      icon: const Icon(Icons.delete, color: AppColors.errorRed),
                      label: const Text(
                        'حذف',
                        style: TextStyle(color: AppColors.errorRed),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // دوال التفاعل
  Future<void> _pickVideo() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.video,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // التحقق من حجم الملف (أقل من 100 ميجا)
        if (file.size > 100 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الفيديو يجب أن يكون أقل من 100 ميجابايت'),
                backgroundColor: AppColors.errorRed,
              ),
            );
          }
          return;
        }

        setState(() {
          _isUploading = true;
          _uploadingType = 'video';
        });

        try {
          final videoUrl = await ref
              .read(lectureContentManagementProvider.notifier)
              .uploadVideo(file, widget.lectureId);

          await _updateContentWithVideo(videoUrl);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم رفع الفيديو بنجاح'),
                backgroundColor: AppColors.successGreen,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في رفع الفيديو: $e'),
                backgroundColor: AppColors.errorRed,
              ),
            );
          }
        } finally {
          if (mounted) {
            setState(() {
              _isUploading = false;
              _uploadingType = null;
            });
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الفيديو: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }

  Future<void> _pickSummary() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // التحقق من حجم الملف (أقل من 10 ميجا)
        if (file.size > 10 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم ملف PDF يجب أن يكون أقل من 10 ميجابايت'),
                backgroundColor: AppColors.errorRed,
              ),
            );
          }
          return;
        }

        setState(() {
          _isUploading = true;
          _uploadingType = 'summary';
        });

        try {
          final summaryUrl = await ref
              .read(lectureContentManagementProvider.notifier)
              .uploadPDF(file, widget.lectureId);

          await _updateContentWithSummary(summaryUrl);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('تم رفع الملخص بنجاح'),
                backgroundColor: AppColors.successGreen,
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('خطأ في رفع الملخص: $e'),
                backgroundColor: AppColors.errorRed,
              ),
            );
          }
        } finally {
          if (mounted) {
            setState(() {
              _isUploading = false;
              _uploadingType = null;
            });
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الملف: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }

  void _removeVideo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف الفيديو؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _updateContentWithVideo(null);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف الفيديو'),
                    backgroundColor: AppColors.successGreen,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }

  void _removeSummary() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف الملخص؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _updateContentWithSummary(null);
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف الملخص'),
                    backgroundColor: AppColors.successGreen,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _showFlashcardDialog() async {
    print('DEBUG: Showing flashcard dialog');
    final result = await showDialog<Flashcard>(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        print('DEBUG: Building flashcard dialog');
        return const FlashcardFormDialog();
      },
    );

    if (result != null) {
      setState(() {
        _localFlashcards.add(result);
        _hasUnsavedChanges = true;
      });
    }
  }

  Future<void> _editFlashcard(int index) async {
    final result = await showDialog<Flashcard>(
      context: context,
      builder: (context) =>
          FlashcardFormDialog(flashcard: _localFlashcards[index], index: index),
    );

    if (result != null) {
      setState(() {
        _localFlashcards[index] = result;
        _hasUnsavedChanges = true;
      });
    }
  }

  void _deleteFlashcard(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذه البطاقة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _localFlashcards.removeAt(index);
                _hasUnsavedChanges = true;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _showQuizDialog() async {
    print('DEBUG: Showing quiz dialog');
    final result = await showDialog<QuizQuestion>(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        print('DEBUG: Building quiz dialog');
        return const McqFormDialog();
      },
    );

    if (result != null) {
      setState(() {
        _localQuizQuestions.add(result);
        _hasUnsavedChanges = true;
      });
    }
  }

  Future<void> _editQuizQuestion(int index) async {
    final result = await showDialog<QuizQuestion>(
      context: context,
      builder: (context) =>
          McqFormDialog(question: _localQuizQuestions[index], index: index),
    );

    if (result != null) {
      setState(() {
        _localQuizQuestions[index] = result;
        _hasUnsavedChanges = true;
      });
    }
  }

  void _deleteQuizQuestion(int index) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا السؤال؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _localQuizQuestions.removeAt(index);
                _hasUnsavedChanges = true;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }

  // دوال التحديث
  Future<void> _updateContentWithVideo(String? videoUrl) async {
    final currentContent = await ref
        .read(adminServiceProvider)
        .getLectureContentOnce(widget.lectureId);

    final updatedContent = (currentContent ??
            LectureContent(
              lectureId: widget.lectureId,
              createdAt: DateTime.now(),
            ))
        .copyWith(videoUrl: videoUrl, updatedAt: DateTime.now());

    await ref
        .read(lectureContentManagementProvider.notifier)
        .updateLectureContent(updatedContent);
  }

  Future<void> _updateContentWithSummary(String? summaryUrl) async {
    final currentContent = await ref
        .read(adminServiceProvider)
        .getLectureContentOnce(widget.lectureId);

    final updatedContent = (currentContent ??
            LectureContent(
              lectureId: widget.lectureId,
              createdAt: DateTime.now(),
            ))
        .copyWith(summaryContent: summaryUrl, updatedAt: DateTime.now());

    await ref
        .read(lectureContentManagementProvider.notifier)
        .updateLectureContent(updatedContent);
  }

  Future<void> _saveAllChanges() async {
    try {
      final currentContent = await ref
          .read(adminServiceProvider)
          .getLectureContentOnce(widget.lectureId);

      final updatedContent = (currentContent ??
              LectureContent(
                lectureId: widget.lectureId,
                createdAt: DateTime.now(),
              ))
          .copyWith(
        flashcards: _localFlashcards,
        quiz: _localQuizQuestions,
        updatedAt: DateTime.now(),
      );

      await ref
          .read(lectureContentManagementProvider.notifier)
          .updateLectureContent(updatedContent);

      setState(() {
        _hasUnsavedChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ جميع التغييرات بنجاح'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التغييرات: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }
}
