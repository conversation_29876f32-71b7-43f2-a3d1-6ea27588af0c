import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../utils/constants.dart';
import '../../providers/admin_provider.dart';
import '../../models/subject.dart';

/// نموذج إضافة/تعديل المادة الدراسية
class SubjectFormDialog extends ConsumerStatefulWidget {
  final Subject? subject;
  final String yearId;

  const SubjectFormDialog({
    super.key,
    this.subject,
    required this.yearId,
  });

  @override
  ConsumerState<SubjectFormDialog> createState() => _SubjectFormDialogState();
}

class _SubjectFormDialogState extends ConsumerState<SubjectFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _orderController = TextEditingController();

  PlatformFile? _selectedImage;
  String? _currentImageUrl;
  bool _isUploading = false;

  bool get isEditing => widget.subject != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _titleController.text = widget.subject!.title;
      _descriptionController.text = widget.subject!.description ?? '';
      _orderController.text = widget.subject!.order?.toString() ?? '';
      _currentImageUrl = widget.subject!.imageUrl;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _orderController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final subjectManagement = ref.watch(subjectManagementProvider);

    return AlertDialog(
      title: Text(isEditing ? 'تعديل المادة الدراسية' : 'إضافة مادة دراسية جديدة'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'اسم المادة *',
                  hintText: 'مثال: علم التشريح',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال اسم المادة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppDimensions.paddingM),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'الوصف (اختياري)',
                  hintText: 'وصف مختصر للمادة',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              TextFormField(
                controller: _orderController,
                decoration: const InputDecoration(
                  labelText: 'ترتيب العرض (اختياري)',
                  hintText: '1, 2, 3...',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final order = int.tryParse(value);
                    if (order == null || order < 1) {
                      return 'يرجى إدخال رقم صحيح أكبر من 0';
                    }
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppDimensions.paddingM),
              _buildImageSection(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: (subjectManagement.isLoading || _isUploading) ? null : _saveSubject,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
          ),
          child: (subjectManagement.isLoading || _isUploading)
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                  ),
                )
              : Text(
                  isEditing ? 'تحديث' : 'إضافة',
                  style: const TextStyle(color: AppColors.white),
                ),
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'صورة المادة (اختياري)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        if (_currentImageUrl != null || _selectedImage != null)
          Container(
            height: 100,
            width: double.infinity,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.lightGray),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: _selectedImage != null
                ? Image.memory(
                    _selectedImage!.bytes!,
                    fit: BoxFit.cover,
                  )
                : _currentImageUrl != null
                    ? Image.network(
                        _currentImageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(Icons.error);
                        },
                      )
                    : const Icon(Icons.image),
          ),
        const SizedBox(height: AppDimensions.paddingS),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _pickImage,
                icon: const Icon(Icons.image),
                label: const Text('اختيار صورة'),
              ),
            ),
            if (_selectedImage != null || _currentImageUrl != null) ...[
              const SizedBox(width: AppDimensions.paddingS),
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedImage = null;
                    _currentImageUrl = null;
                  });
                },
                icon: const Icon(Icons.delete, color: AppColors.errorRed),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        
        // التحقق من حجم الملف (أقل من 5 ميجا)
        if (file.size > 5 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة يجب أن يكون أقل من 5 ميجابايت'),
                backgroundColor: AppColors.errorRed,
              ),
            );
          }
          return;
        }

        setState(() {
          _selectedImage = file;
          _currentImageUrl = null;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }

  Future<void> _saveSubject() async {
    if (!_formKey.currentState!.validate()) return;

    final title = _titleController.text.trim();
    final description = _descriptionController.text.trim();
    final order = int.tryParse(_orderController.text.trim());

    setState(() {
      _isUploading = true;
    });

    try {
      String? imageUrl = _currentImageUrl;

      // رفع الصورة إذا تم اختيار صورة جديدة
      if (_selectedImage != null) {
        final subjectId = isEditing 
            ? widget.subject!.id 
            : ref.read(adminServiceProvider).generateId();
        
        imageUrl = await ref
            .read(lectureContentManagementProvider.notifier)
            .uploadImage(_selectedImage!, 'subjects', subjectId);
      }

      final subject = Subject(
        id: isEditing ? widget.subject!.id : ref.read(adminServiceProvider).generateId(),
        title: title,
        description: description.isEmpty ? null : description,
        yearId: widget.yearId,
        order: order,
        imageUrl: imageUrl,
        createdAt: isEditing ? widget.subject!.createdAt : DateTime.now(),
        updatedAt: isEditing ? DateTime.now() : null,
      );

      if (isEditing) {
        await ref.read(subjectManagementProvider.notifier).updateSubject(subject);
      } else {
        await ref.read(subjectManagementProvider.notifier).addSubject(subject);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEditing ? 'تم تحديث المادة بنجاح' : 'تم إضافة المادة بنجاح'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }
}
