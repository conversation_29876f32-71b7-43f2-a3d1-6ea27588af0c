import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../utils/constants.dart';
import '../../providers/admin_provider.dart';
import '../../models/lecture.dart';

/// نموذج إضافة/تعديل المحاضرة
class LectureFormDialog extends ConsumerStatefulWidget {
  final Lecture? lecture;
  final String courseId;

  const LectureFormDialog({
    super.key,
    this.lecture,
    required this.courseId,
  });

  @override
  ConsumerState<LectureFormDialog> createState() => _LectureFormDialogState();
}

class _LectureFormDialogState extends ConsumerState<LectureFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _orderController = TextEditingController();
  final _durationController = TextEditingController();

  bool _hasVideo = false;
  bool _hasSummary = false;
  bool _hasFlashcards = false;
  bool _hasQuiz = false;

  bool get isEditing => widget.lecture != null;

  @override
  void initState() {
    super.initState();
    if (isEditing) {
      _titleController.text = widget.lecture!.title;
      _descriptionController.text = widget.lecture!.description ?? '';
      _orderController.text = widget.lecture!.order?.toString() ?? '';
      _durationController.text = widget.lecture!.duration?.toString() ?? '';
      _hasVideo = widget.lecture!.hasVideo;
      _hasSummary = widget.lecture!.hasSummary;
      _hasFlashcards = widget.lecture!.hasFlashcards;
      _hasQuiz = widget.lecture!.hasQuiz;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _orderController.dispose();
    _durationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final lectureManagement = ref.watch(lectureManagementProvider);

    return AlertDialog(
      title: Text(isEditing ? 'تعديل المحاضرة' : 'إضافة محاضرة جديدة'),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان المحاضرة *',
                  hintText: 'مثال: مقدمة في علم التشريح',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'يرجى إدخال عنوان المحاضرة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: AppDimensions.paddingM),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'وصف المحاضرة (اختياري)',
                  hintText: 'وصف مختصر للمحاضرة',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _orderController,
                      decoration: const InputDecoration(
                        labelText: 'ترتيب العرض',
                        hintText: '1, 2, 3...',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final order = int.tryParse(value);
                          if (order == null || order < 1) {
                            return 'رقم صحيح أكبر من 0';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  Expanded(
                    child: TextFormField(
                      controller: _durationController,
                      decoration: const InputDecoration(
                        labelText: 'المدة (دقيقة)',
                        hintText: '30',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.isNotEmpty) {
                          final duration = int.tryParse(value);
                          if (duration == null || duration < 1) {
                            return 'رقم صحيح أكبر من 0';
                          }
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              const Align(
                alignment: Alignment.centerRight,
                child: Text(
                  'محتوى المحاضرة:',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: AppDimensions.paddingS),
              _buildContentCheckboxes(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: lectureManagement.isLoading ? null : _saveLecture,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryBlue,
          ),
          child: lectureManagement.isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(AppColors.white),
                  ),
                )
              : Text(
                  isEditing ? 'تحديث' : 'إضافة',
                  style: const TextStyle(color: AppColors.white),
                ),
        ),
      ],
    );
  }

  Widget _buildContentCheckboxes() {
    return Column(
      children: [
        CheckboxListTile(
          title: const Text('يحتوي على فيديو'),
          subtitle: const Text('فيديو تعليمي للمحاضرة'),
          value: _hasVideo,
          onChanged: (value) {
            setState(() {
              _hasVideo = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: const Text('يحتوي على ملخص'),
          subtitle: const Text('ملخص مكتوب أو ملف PDF'),
          value: _hasSummary,
          onChanged: (value) {
            setState(() {
              _hasSummary = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: const Text('يحتوي على بطاقات تعليمية'),
          subtitle: const Text('Flashcards للمراجعة'),
          value: _hasFlashcards,
          onChanged: (value) {
            setState(() {
              _hasFlashcards = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
        CheckboxListTile(
          title: const Text('يحتوي على اختبار'),
          subtitle: const Text('أسئلة اختيار متعدد'),
          value: _hasQuiz,
          onChanged: (value) {
            setState(() {
              _hasQuiz = value ?? false;
            });
          },
          controlAffinity: ListTileControlAffinity.leading,
        ),
      ],
    );
  }

  Future<void> _saveLecture() async {
    if (!_formKey.currentState!.validate()) return;

    final title = _titleController.text.trim();
    final description = _descriptionController.text.trim();
    final order = int.tryParse(_orderController.text.trim());
    final duration = int.tryParse(_durationController.text.trim());

    try {
      final lecture = Lecture(
        id: isEditing ? widget.lecture!.id : ref.read(adminServiceProvider).generateId(),
        title: title,
        description: description.isEmpty ? null : description,
        courseId: widget.courseId,
        order: order,
        duration: duration,
        hasVideo: _hasVideo,
        hasSummary: _hasSummary,
        hasFlashcards: _hasFlashcards,
        hasQuiz: _hasQuiz,
        createdAt: isEditing ? widget.lecture!.createdAt : DateTime.now(),
        updatedAt: isEditing ? DateTime.now() : null,
      );

      if (isEditing) {
        await ref.read(lectureManagementProvider.notifier).updateLecture(lecture);
      } else {
        await ref.read(lectureManagementProvider.notifier).addLecture(lecture);
      }

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEditing ? 'تم تحديث المحاضرة بنجاح' : 'تم إضافة المحاضرة بنجاح'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }
}
