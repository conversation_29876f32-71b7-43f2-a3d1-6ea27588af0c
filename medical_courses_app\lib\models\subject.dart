import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج المادة الدراسية
class Subject {
  final String id;
  final String title;
  final String? description;
  final String yearId;
  final int? order;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const Subject({
    required this.id,
    required this.title,
    this.description,
    required this.yearId,
    this.order,
    this.imageUrl,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء Subject من JSON
  factory Subject.fromJson(Map<String, dynamic> json) {
    return Subject(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      yearId: json['yearId'] as String,
      order: json['order'] as int?,
      imageUrl: json['imageUrl'] as String?,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: json['updatedAt'] != null 
          ? (json['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// إنشاء Subject من DocumentSnapshot
  factory Subject.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Subject(
      id: doc.id,
      title: data['title'] as String,
      description: data['description'] as String?,
      yearId: data['yearId'] as String,
      order: data['order'] as int?,
      imageUrl: data['imageUrl'] as String?,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// تحويل Subject إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'yearId': yearId,
      'order': order,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// تحويل Subject إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'yearId': yearId,
      'order': order,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// إنشاء نسخة محدثة من Subject
  Subject copyWith({
    String? id,
    String? title,
    String? description,
    String? yearId,
    int? order,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Subject(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      yearId: yearId ?? this.yearId,
      order: order ?? this.order,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Subject(id: $id, title: $title, yearId: $yearId, order: $order)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Subject && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
