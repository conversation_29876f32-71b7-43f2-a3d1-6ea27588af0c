import 'package:flutter/material.dart';
import '../../utils/constants.dart';

/// شاشة المحاضرة
class LectureScreen extends StatelessWidget {
  final String courseId;
  final String lectureId;

  const LectureScreen({
    super.key,
    required this.courseId,
    required this.lectureId,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('المحاضرة'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.play_circle_outline,
              size: 64,
              color: AppColors.primaryBlue,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'هذه شاشة المحاضرة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.darkText,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              'معرف الكورس: $courseId',
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              'معرف المحاضرة: $lectureId',
              style: const TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'سيتم عرض محتوى المحاضرة هنا',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
