import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../utils/constants.dart';
import '../../providers/admin_provider.dart';
import '../../models/lecture.dart';
import '../../widgets/admin/lecture_form_dialog.dart';

/// شاشة إدارة المحاضرات ضمن كورس
class ManageLecturesScreen extends ConsumerWidget {
  final String courseId;

  const ManageLecturesScreen({
    super.key,
    required this.courseId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lecturesAsync = ref.watch(lecturesProvider(courseId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المحاضرات'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showLectureDialog(context, ref),
          ),
        ],
      ),
      body: lecturesAsync.when(
        data: (lectures) => _buildLecturesList(context, ref, lectures),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.errorRed,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Text(
                'خطأ في تحميل المحاضرات',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              ElevatedButton(
                onPressed: () => ref.refresh(lecturesProvider(courseId)),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showLectureDialog(context, ref),
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.add, color: AppColors.white),
      ),
    );
  }

  Widget _buildLecturesList(
      BuildContext context, WidgetRef ref, List<Lecture> lectures) {
    if (lectures.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.play_lesson,
              size: 64,
              color: AppColors.lightText,
            ),
            SizedBox(height: AppDimensions.paddingM),
            Text(
              'لا توجد محاضرات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.lightText,
              ),
            ),
            SizedBox(height: AppDimensions.paddingS),
            Text(
              'اضغط على زر + لإضافة محاضرة جديدة',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      itemCount: lectures.length,
      itemBuilder: (context, index) {
        final lecture = lectures[index];
        return _buildLectureCard(context, ref, lecture, index + 1);
      },
    );
  }

  Widget _buildLectureCard(
      BuildContext context, WidgetRef ref, Lecture lecture, int displayOrder) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: 4,
      child: InkWell(
        onTap: () => context.go('/admin/lecture_content/${lecture.id}'),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // رقم المحاضرة
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.primaryBlue,
                      borderRadius:
                          BorderRadius.circular(AppDimensions.radiusS),
                    ),
                    child: Center(
                      child: Text(
                        '${lecture.order ?? displayOrder}',
                        style: const TextStyle(
                          color: AppColors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: AppDimensions.paddingM),
                  // معلومات المحاضرة
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          lecture.title,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        if (lecture.description != null) ...[
                          const SizedBox(height: AppDimensions.paddingXS),
                          Text(
                            lecture.description!,
                            style: const TextStyle(
                              fontSize: 14,
                              color: AppColors.lightText,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                        const SizedBox(height: AppDimensions.paddingS),
                        Row(
                          children: [
                            if (lecture.duration != null) ...[
                              const Icon(
                                Icons.access_time,
                                size: 16,
                                color: AppColors.lightText,
                              ),
                              const SizedBox(width: AppDimensions.paddingXS),
                              Text(
                                '${lecture.duration} دقيقة',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: AppColors.lightText,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  // قائمة الخيارات
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _showLectureDialog(context, ref, lecture: lecture);
                          break;
                        case 'content':
                          context.go('/admin/lecture_content/${lecture.id}');
                          break;
                        case 'delete':
                          _showDeleteDialog(context, ref, lecture);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: ListTile(
                          leading: Icon(Icons.edit),
                          title: Text('تعديل'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'content',
                        child: ListTile(
                          leading: Icon(Icons.video_library),
                          title: Text('إدارة المحتوى'),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: ListTile(
                          leading:
                              Icon(Icons.delete, color: AppColors.errorRed),
                          title: Text('حذف',
                              style: TextStyle(color: AppColors.errorRed)),
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: AppDimensions.paddingM),
              // مؤشرات المحتوى
              _buildContentIndicators(lecture),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContentIndicators(Lecture lecture) {
    return Wrap(
      spacing: AppDimensions.paddingS,
      runSpacing: AppDimensions.paddingXS,
      children: [
        _buildContentChip(
          'فيديو',
          Icons.play_circle,
          lecture.hasVideo,
        ),
        _buildContentChip(
          'ملخص',
          Icons.description,
          lecture.hasSummary,
        ),
        _buildContentChip(
          'بطاقات',
          Icons.quiz,
          lecture.hasFlashcards,
        ),
        _buildContentChip(
          'اختبار',
          Icons.assignment,
          lecture.hasQuiz,
        ),
      ],
    );
  }

  Widget _buildContentChip(String label, IconData icon, bool hasContent) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingS,
        vertical: AppDimensions.paddingXS,
      ),
      decoration: BoxDecoration(
        color: hasContent
            ? AppColors.medicalGreen.withValues(alpha: 0.1)
            : AppColors.lightGray.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(AppDimensions.radiusS),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: hasContent ? AppColors.medicalGreen : AppColors.lightText,
          ),
          const SizedBox(width: AppDimensions.paddingXS),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: hasContent ? AppColors.medicalGreen : AppColors.lightText,
              fontWeight: hasContent ? FontWeight.w500 : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  void _showLectureDialog(BuildContext context, WidgetRef ref,
      {Lecture? lecture}) {
    showDialog(
      context: context,
      builder: (context) => LectureFormDialog(
        lecture: lecture,
        courseId: courseId,
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Lecture lecture) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
            'هل أنت متأكد من حذف المحاضرة "${lecture.title}"؟\nسيتم حذف جميع المحتوى المرتبط بها.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref
                  .read(lectureManagementProvider.notifier)
                  .deleteLecture(lecture.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }
}
