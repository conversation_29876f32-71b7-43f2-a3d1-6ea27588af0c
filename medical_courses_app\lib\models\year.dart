import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج السنة الدراسية
class Year {
  final String id;
  final String title;
  final String? description;
  final int? order;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const Year({
    required this.id,
    required this.title,
    this.description,
    this.order,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء Year من JSON
  factory Year.fromJson(Map<String, dynamic> json) {
    return Year(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      order: json['order'] as int?,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: json['updatedAt'] != null 
          ? (json['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// إنشاء Year من DocumentSnapshot
  factory Year.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Year(
      id: doc.id,
      title: data['title'] as String,
      description: data['description'] as String?,
      order: data['order'] as int?,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// تحويل Year إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'order': order,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// تحويل Year إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'order': order,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// إنشاء نسخة محدثة من Year
  Year copyWith({
    String? id,
    String? title,
    String? description,
    int? order,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Year(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Year(id: $id, title: $title, description: $description, order: $order)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Year && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
