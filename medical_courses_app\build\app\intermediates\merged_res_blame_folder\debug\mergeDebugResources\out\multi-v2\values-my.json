{"logs": [{"outputFile": "com.medicalcourses.app-mergeDebugResources-35:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\90b123502cca9071887a149ae2db5355\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "149", "endOffsets": "344"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2057", "endColumns": "153", "endOffsets": "2206"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "4,5,6,7,8,9,10,42", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "287,390,494,597,699,804,910,4540", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "385,489,592,694,799,905,1024,4636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2694643560e27514fec2bb7aa58bb43a\\transformed\\browser-1.4.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,164,270,385", "endColumns": "108,105,114,106", "endOffsets": "159,265,380,487"}, "to": {"startLines": "29,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3383,4212,4318,4433", "endColumns": "108,105,114,106", "endOffsets": "3487,4313,4428,4535"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8798035275722523399db6d5c2b9413f\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,130,202,275,344,426,501,602,697", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "125,197,270,339,421,496,597,692,770"}, "to": {"startLines": "30,31,32,33,34,35,36,37,38", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3492,3567,3639,3712,3781,3863,3938,4039,4134", "endColumns": "74,71,72,68,81,74,100,94,77", "endOffsets": "3562,3634,3707,3776,3858,3933,4034,4129,4207"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c1afa744a109aa55a636f9b508afdb55\\transformed\\jetified-play-services-base-18.1.0\\res\\values-my\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,586,693,836,964,1083,1189,1361,1463,1629,1768,1922,2105,2171,2240", "endColumns": "102,159,129,106,142,127,118,105,171,101,165,138,153,182,65,68,84", "endOffsets": "295,455,585,692,835,963,1082,1188,1360,1462,1628,1767,1921,2104,2170,2239,2324"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1029,1136,1300,1434,1545,1692,1824,1947,2211,2387,2493,2663,2806,2964,3151,3221,3294", "endColumns": "106,163,133,110,146,131,122,109,175,105,169,142,157,186,69,72,88", "endOffsets": "1131,1295,1429,1540,1687,1819,1942,2052,2382,2488,2658,2801,2959,3146,3216,3289,3378"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1203ed00ac77ef6e80766f7044873004\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,119", "endOffsets": "162,282"}}]}]}