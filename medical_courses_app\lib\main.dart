import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:firebase_core/firebase_core.dart';
import 'utils/app_theme.dart';
import 'utils/app_router.dart';
import 'utils/constants.dart';

/// نقطة دخول التطبيق الرئيسية
void main() async {
  // التأكد من تهيئة Flutter
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Firebase
  // ملاحظة: يجب إضافة ملفات التكوين التالية:
  // - android/app/google-services.json (للأندرويد)
  // - ios/Runner/GoogleService-Info.plist (لـ iOS)
  try {
    await Firebase.initializeApp();
  } catch (e) {
    // في حالة عدم وجود ملفات Firebase، سيتم تشغيل التطبيق بدونها
    debugPrint('تحذير: لم يتم تهيئة Firebase - $e');
  }

  // إعداد اتجاه الشاشة (عمودي فقط)
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // إعداد شريط الحالة
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.light,
      systemNavigationBarColor: AppColors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // تشغيل التطبيق مع Riverpod
  runApp(const ProviderScope(child: MedicalCoursesApp()));
}

/// التطبيق الرئيسي
class MedicalCoursesApp extends ConsumerWidget {
  const MedicalCoursesApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // الحصول على مزود التوجيه
    final router = ref.watch(routerProvider);

    return MaterialApp.router(
      // معلومات التطبيق
      title: AppStrings.appName,
      debugShowCheckedModeBanner: false,

      // إعدادات التوطين
      locale: const Locale('ar', 'SA'), // العربية السعودية كافتراضي
      supportedLocales: const [
        Locale('ar', 'SA'), // العربية
        Locale('en', 'US'), // الإنجليزية
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      // الثيمات
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system, // اتباع إعدادات النظام
      // التوجيه
      routerConfig: router,

      // إعدادات إضافية
      builder: (context, child) {
        return Directionality(
          textDirection: TextDirection.rtl, // اتجاه النص من اليمين لليسار
          child: child ?? const SizedBox.shrink(),
        );
      },
    );
  }
}
