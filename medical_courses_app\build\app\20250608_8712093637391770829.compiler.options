"-Xallow-no-source-files" "-classpath" "E:\\medcourses\\medical_courses_app\\build\\app\\intermediates\\compile_and_runtime_not_namespaced_r_class_jar\\debug\\processDebugResources\\R.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9fa4c71d004ca7a0a5d58d12844ea051\\transformed\\jetified-libs.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\997c71003b88b692b113760365ef0cc0\\transformed\\jetified-arm64_v8a_debug-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar;E:\\medcourses\\medical_courses_app\\build\\cloud_firestore\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\file_picker\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\firebase_auth\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\firebase_storage\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\firebase_core\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\flutter_pdfview\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\flutter_plugin_android_lifecycle\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\image_picker_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\package_info_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\video_player_android\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;E:\\medcourses\\medical_courses_app\\build\\wakelock_plus\\intermediates\\compile_library_classes_jar\\debug\\bundleLibCompileToJarDebug\\classes.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b2c27a80e0c20027f562483910df2b9a\\transformed\\jetified-firebase-auth-ktx-23.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cb0261d529503ffba9bdd3db890a38dc\\transformed\\jetified-firebase-firestore-ktx-25.1.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f8fe923a6c6cce89e4c50a7e194281c1\\transformed\\jetified-firebase-storage-ktx-21.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fe66e2acfd65cccb448d7acb2927a9e\\transformed\\jetified-firebase-auth-23.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\924665bdced37d650a03430767cb620f\\transformed\\jetified-firebase-firestore-25.1.4-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1ae3e35c57a2c4a33f771f301ae079f6\\transformed\\jetified-firebase-storage-21.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2f0ec6871faf56a4488c012f1ea5d2ac\\transformed\\jetified-firebase-appcheck-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0e978e6460fdc7839a7e7b5a0f9b844a\\transformed\\jetified-firebase-common-ktx-21.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\39acd289c161f891c4ad94818faf2ca8\\transformed\\jetified-kotlin-stdlib-jdk7-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c22940fd007c288ac2172fb605aed0f1\\transformed\\jetified-flutter_embedding_debug-1.0.0-18818009497c581ede5d8a3b8b833b81d00cebb7.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\9d0d070fe2cd0241da27f11835a715f7\\transformed\\jetified-play-services-auth-api-phone-18.0.2-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2d43847cfac42394cca3b92e1b186d68\\transformed\\jetified-recaptcha-18.6.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0ba31104e0971eebd383a9638767a271\\transformed\\jetified-integrity-1.3.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\74e31390fbd633534971c5c9b603ef08\\transformed\\jetified-firebase-appcheck-interop-17.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ea488da97a2a794bc25bb89d3361b43d\\transformed\\jetified-firebase-auth-interop-20.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f3b77c8345157cb5f33f625bd352e2d\\transformed\\jetified-firebase-common-21.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\97724ced2d76c807da51dbdfe4264718\\transformed\\jetified-activity-1.9.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\df5d28f461ffd56f36cdd72acdfc344f\\transformed\\loader-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\d6f4d647e17b29c5423a0c7fa8c98f2d\\transformed\\jetified-lifecycle-livedata-core-ktx-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ae7f7f186029d473ac3dbfcddcd85b22\\transformed\\lifecycle-livedata-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\115b656e1a4aecf880260d6d83623996\\transformed\\lifecycle-viewmodel-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c315d47e2f7879c3deeb8a2382af17bc\\transformed\\lifecycle-livedata-core-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\890fa23f232b0e27acd86381ae22e986\\transformed\\jetified-lifecycle-viewmodel-savedstate-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ca08bea8da6ce44b915b59db148eb4a9\\transformed\\jetified-window-java-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\fff59de3233a53a251731adb4c6ced0a\\transformed\\jetified-window-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e789c606285d17c2ec99c03f43d33c98\\transformed\\jetified-kotlinx-coroutines-android-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7ead9a6c3bb927c55881788d3824402d\\transformed\\jetified-kotlinx-coroutines-core-jvm-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ffa71309901bef385c9cef8c3144adc6\\transformed\\jetified-kotlinx-coroutines-play-services-1.7.3.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\6ebc2ef591e2be596982fae411343ec7\\transformed\\jetified-firebase-database-collection-18.0.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\888c73e63cf1c419ede361805acaaaa0\\transformed\\jetified-play-services-base-18.1.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2e9664f97d9a18f769586f24fdee615a\\transformed\\jetified-play-services-tasks-18.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b04f3adc919a72f10a7cbc6c625ebbd3\\transformed\\jetified-play-services-basement-18.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\f0c43bb5fcbf5d527acc938eed5c5ee7\\transformed\\fragment-1.7.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7e58e72c8f0ccee9f82f571a2fda76e5\\transformed\\browser-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\496b2ffad9f48ea28eeff97372eecba8\\transformed\\jetified-core-ktx-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0718b469eda36dc9592c0463fc44f0f5\\transformed\\viewpager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\aaa838aa6779c7b3265d9262098da713\\transformed\\customview-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\15ba965be5d36f1409c507c4f4f01060\\transformed\\core-1.13.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8072e09447510b0de763fa662fba307a\\transformed\\lifecycle-runtime-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4fd0b64dbcc747184e3367538d3940e0\\transformed\\jetified-lifecycle-process-2.7.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common-java8\\2.7.0\\2ad14aed781c4a73ed4dbb421966d408a0a06686\\lifecycle-common-java8-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.lifecycle\\lifecycle-common\\2.7.0\\85334205d65cca70ed0109c3acbd29e22a2d9cb1\\lifecycle-common-2.7.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\701ba9da7cfb401b1a4f941fa4de1e39\\transformed\\jetified-credentials-play-services-auth-1.2.0-rc01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c63dc469e5da58d11c1b177d3e86c142\\transformed\\jetified-credentials-1.2.0-rc01-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2d84b4d753bef46c4f0c33072ed597d9\\transformed\\jetified-annotation-experimental-1.4.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\546c29fc3b12ba0ada2deef99fc20985\\transformed\\jetified-savedstate-1.2.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\82f50a1147ea7962ad05f59f739641fb\\transformed\\versionedparcelable-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.collection\\collection\\1.2.0\\34dbc21d203cc4d4d623ac572a21acd4ccd716af\\collection-1.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\43a9ede848ece583b5fbea95753bc161\\transformed\\localbroadcastmanager-1.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\7f1860f1dedc07b8c1db1817385fde25\\transformed\\core-runtime-2.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\androidx.arch.core\\core-common\\2.2.0\\5e1b8b81dfd5f52c56a8d53b18ca759c19a301f3\\core-common-2.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cfe501695087fe013590264201716ccd\\transformed\\jetified-annotation-jvm-1.9.1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ade16677ec891dea4619d807ecb70bab\\transformed\\jetified-kotlin-stdlib-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a708d9fd658ffd90d23256f3459b5b61\\transformed\\jetified-kotlin-stdlib-jdk8-2.1.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\a1a24e07e8946987328cdbf73d5c0d1c\\transformed\\jetified-annotations-23.0.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\51357615e5825f038c2637fd58fd16d8\\transformed\\jetified-listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\cd60595a499747cfa90d326929eabb59\\transformed\\jetified-startup-runtime-1.1.1-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e2cbe243dd93764f3ea77e6c23ccd98f\\transformed\\jetified-tracing-1.2.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\e9b80c786d1f3bf9007353926f4b6eb1\\transformed\\jetified-relinker-1.4.5-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ad8e1b32659e80bf9a8534120fdfaa76\\transformed\\jetified-core-common-2.0.3-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\23278c4356972f2995cc2c88ffc4ad85\\transformed\\jetified-firebase-components-18.0.0-api.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\bdb45e5b04ad6f83275e0d516fa0ae72\\transformed\\jetified-firebase-annotations-16.2.0.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\4754485aaf1dc7a247c71909b416b8fc\\transformed\\jetified-javax.inject-1.jar;C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\ec31cc836dc59eded10f273dda694ba5\\transformed\\jetified-protolite-well-known-types-18.0.1-api.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\platforms\\android-36\\android.jar;C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\build-tools\\34.0.0\\core-lambda-stubs.jar" "-d" "E:\\medcourses\\medical_courses_app\\build\\app\\tmp\\kotlin-classes\\debug" "-jvm-target" "17" "-module-name" "app_debug" "-no-jdk" "-no-reflect" "-no-stdlib" "E:\\medcourses\\medical_courses_app\\android\\app\\src\\main\\java\\io\\flutter\\plugins\\GeneratedPluginRegistrant.java" "E:\\medcourses\\medical_courses_app\\android\\app\\src\\main\\kotlin\\com\\medicalcourses\\app\\MainActivity.kt"