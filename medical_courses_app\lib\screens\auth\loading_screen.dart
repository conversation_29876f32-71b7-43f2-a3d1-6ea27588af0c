import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../utils/constants.dart';

/// شاشة التحميل الأولية للتطبيق
class LoadingScreen extends ConsumerStatefulWidget {
  const LoadingScreen({super.key});

  @override
  ConsumerState<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends ConsumerState<LoadingScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // إعداد الرسوم المتحركة
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    // بدء الرسوم المتحركة
    _animationController.forward();

    // محاكاة عملية التحميل
    _initializeApp();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// تهيئة التطبيق
  Future<void> _initializeApp() async {
    try {
      // محاكاة عمليات التهيئة
      await Future.delayed(const Duration(seconds: 3));

      // TODO: إضافة منطق التهيئة الفعلي هنا
      // - تهيئة Firebase
      // - فحص حالة المصادقة
      // - تحميل الإعدادات
      // - فحص الاتصال بالإنترنت
      // - التحقق من التحديثات

      // بعد اكتمال التهيئة، الانتقال إلى الشاشة المناسبة
      if (mounted) {
        // TODO: استبدال هذا بالتنقل الفعلي بناءً على حالة المصادقة
        // context.go('/login'); // إذا لم يكن مسجل دخول
        // context.go('/student'); // إذا كان طالب
        // context.go('/admin'); // إذا كان مدير

        // حالياً، البقاء في شاشة التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحميل التطبيق بنجاح! (شاشة مؤقتة)'),
            backgroundColor: AppColors.successGreen,
          ),
        );
      }
    } catch (error) {
      // معالجة أخطاء التهيئة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التطبيق: $error'),
            backgroundColor: AppColors.errorRed,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: AppColors.white,
              onPressed: _initializeApp,
            ),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // شعار التطبيق
              AnimatedBuilder(
                animation: _animationController,
                builder: (context, child) {
                  return FadeTransition(
                    opacity: _fadeAnimation,
                    child: ScaleTransition(
                      scale: _scaleAnimation,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          color: AppColors.primaryBlue,
                          borderRadius:
                              BorderRadius.circular(AppDimensions.radiusXL),
                          boxShadow: [
                            BoxShadow(
                              color:
                                  AppColors.primaryBlue.withValues(alpha: 0.3),
                              blurRadius: 20,
                              offset: const Offset(0, 10),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.medical_services,
                          size: 60,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  );
                },
              ),

              const SizedBox(height: AppDimensions.paddingXL),

              // اسم التطبيق
              FadeTransition(
                opacity: _fadeAnimation,
                child: Text(
                  AppStrings.appName,
                  style: Theme.of(context).textTheme.displaySmall?.copyWith(
                        color: AppColors.primaryBlue,
                        fontWeight: FontWeight.bold,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: AppDimensions.paddingS),

              // وصف التطبيق
              FadeTransition(
                opacity: _fadeAnimation,
                child: Text(
                  'منصة تعليمية متقدمة للمحتوى الطبي',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: AppColors.lightText,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),

              const SizedBox(height: AppDimensions.paddingXL * 2),

              // مؤشر التحميل
              FadeTransition(
                opacity: _fadeAnimation,
                child: Column(
                  children: [
                    const CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
                      strokeWidth: 3,
                    ),
                    const SizedBox(height: AppDimensions.paddingM),
                    Text(
                      AppStrings.loading,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppColors.lightText,
                          ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppDimensions.paddingXL * 2),

              // معلومات الإصدار
              FadeTransition(
                opacity: _fadeAnimation,
                child: Text(
                  'الإصدار 1.0.0',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppColors.lightText,
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
