import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../models/user.dart' as app_user;
import '../services/auth_service.dart';

/// حالة المصادقة
enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  error,
}

/// فئة حالة المصادقة
class AuthStateData {
  final AuthState state;
  final app_user.User? user;
  final String? errorMessage;

  const AuthStateData({
    required this.state,
    this.user,
    this.errorMessage,
  });

  AuthStateData copyWith({
    AuthState? state,
    app_user.User? user,
    String? errorMessage,
  }) {
    return AuthStateData(
      state: state ?? this.state,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  bool get isLoading => state == AuthState.loading;
  bool get isAuthenticated => state == AuthState.authenticated && user != null;
  bool get isUnauthenticated => state == AuthState.unauthenticated;
  bool get hasError => state == AuthState.error;
}

/// مزود حالة المصادقة
class AuthNotifier extends StateNotifier<AuthStateData> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthStateData(state: AuthState.initial)) {
    _init();
  }

  /// تهيئة المزود والاستماع لتغييرات المصادقة
  void _init() {
    _authService.authStateChanges.listen((firebase_auth.User? firebaseUser) async {
      if (firebaseUser == null) {
        state = const AuthStateData(state: AuthState.unauthenticated);
      } else {
        try {
          final user = await _authService.getCurrentUser();
          if (user != null) {
            state = AuthStateData(state: AuthState.authenticated, user: user);
          } else {
            state = const AuthStateData(state: AuthState.unauthenticated);
          }
        } catch (e) {
          state = AuthStateData(
            state: AuthState.error,
            errorMessage: 'خطأ في تحميل بيانات المستخدم: $e',
          );
        }
      }
    });
  }

  /// تسجيل مستخدم جديد
  Future<void> signUp({
    required String name,
    required String email,
    required String password,
    String role = 'student',
  }) async {
    state = state.copyWith(state: AuthState.loading);
    
    try {
      final user = await _authService.signUp(
        name: name,
        email: email,
        password: password,
        role: role,
      );
      
      if (user != null) {
        state = AuthStateData(state: AuthState.authenticated, user: user);
      } else {
        state = const AuthStateData(
          state: AuthState.error,
          errorMessage: 'فشل في إنشاء الحساب',
        );
      }
    } catch (e) {
      state = AuthStateData(
        state: AuthState.error,
        errorMessage: e.toString(),
      );
    }
  }

  /// تسجيل الدخول
  Future<void> signIn({
    required String email,
    required String password,
  }) async {
    state = state.copyWith(state: AuthState.loading);
    
    try {
      final user = await _authService.signIn(
        email: email,
        password: password,
      );
      
      if (user != null) {
        state = AuthStateData(state: AuthState.authenticated, user: user);
      } else {
        state = const AuthStateData(
          state: AuthState.error,
          errorMessage: 'فشل في تسجيل الدخول',
        );
      }
    } catch (e) {
      state = AuthStateData(
        state: AuthState.error,
        errorMessage: e.toString(),
      );
    }
  }

  /// تسجيل الخروج
  Future<void> signOut() async {
    state = state.copyWith(state: AuthState.loading);
    
    try {
      await _authService.signOut();
      state = const AuthStateData(state: AuthState.unauthenticated);
    } catch (e) {
      state = AuthStateData(
        state: AuthState.error,
        errorMessage: e.toString(),
      );
    }
  }

  /// إعادة تعيين كلمة المرور
  Future<void> resetPassword(String email) async {
    try {
      await _authService.resetPassword(email);
    } catch (e) {
      state = AuthStateData(
        state: AuthState.error,
        errorMessage: e.toString(),
      );
      rethrow;
    }
  }

  /// تحديث بيانات المستخدم
  Future<void> updateUser(app_user.User updatedUser) async {
    try {
      await _authService.updateUserData(updatedUser);
      state = state.copyWith(user: updatedUser);
    } catch (e) {
      state = AuthStateData(
        state: AuthState.error,
        errorMessage: e.toString(),
      );
    }
  }

  /// مسح رسالة الخطأ
  void clearError() {
    if (state.hasError) {
      state = state.copyWith(
        state: state.user != null ? AuthState.authenticated : AuthState.unauthenticated,
        errorMessage: null,
      );
    }
  }
}

/// مزود خدمة المصادقة
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

/// مزود حالة المصادقة
final authProvider = StateNotifierProvider<AuthNotifier, AuthStateData>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthNotifier(authService);
});
