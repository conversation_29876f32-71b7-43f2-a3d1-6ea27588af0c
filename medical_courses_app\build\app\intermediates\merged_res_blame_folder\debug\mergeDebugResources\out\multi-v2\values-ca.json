{"logs": [{"outputFile": "com.medicalcourses.app-mergeDebugResources-35:/values-ca/values-ca.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\c1afa744a109aa55a636f9b508afdb55\\transformed\\jetified-play-services-base-18.1.0\\res\\values-ca\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,442,565,670,816,939,1058,1162,1329,1434,1589,1716,1876,2030,2091,2155", "endColumns": "100,147,122,104,145,122,118,103,166,104,154,126,159,153,60,63,82", "endOffsets": "293,441,564,669,815,938,1057,1161,1328,1433,1588,1715,1875,2029,2090,2154,2237"}, "to": {"startLines": "11,12,13,14,15,16,17,18,20,21,22,23,24,25,26,27,28", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1021,1126,1278,1405,1514,1664,1791,1914,2157,2328,2437,2596,2727,2891,3049,3114,3182", "endColumns": "104,151,126,108,149,126,122,107,170,108,158,130,163,157,64,67,86", "endOffsets": "1121,1273,1400,1509,1659,1786,1909,2017,2323,2432,2591,2722,2886,3044,3109,3177,3264"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\73aa083afefb941d18007d1b70cec6be\\transformed\\core-1.13.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,352,449,555,660,786", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "146,248,347,444,550,655,781,882"}, "to": {"startLines": "4,5,6,7,8,9,10,42", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "290,386,488,587,684,790,895,4408", "endColumns": "95,101,98,96,105,104,125,100", "endOffsets": "381,483,582,679,785,890,1016,4504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\2694643560e27514fec2bb7aa58bb43a\\transformed\\browser-1.4.0\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,168,271,382", "endColumns": "112,102,110,108", "endOffsets": "163,266,377,486"}, "to": {"startLines": "29,39,40,41", "startColumns": "4,4,4,4", "startOffsets": "3269,4085,4188,4299", "endColumns": "112,102,110,108", "endOffsets": "3377,4183,4294,4403"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\90b123502cca9071887a149ae2db5355\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ca\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "130", "endOffsets": "325"}, "to": {"startLines": "19", "startColumns": "4", "startOffsets": "2022", "endColumns": "134", "endOffsets": "2152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\1203ed00ac77ef6e80766f7044873004\\transformed\\jetified-credentials-1.2.0-rc01\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,119", "endOffsets": "165,285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\8798035275722523399db6d5c2b9413f\\transformed\\jetified-media3-exoplayer-1.4.1\\res\\values-ca\\values-ca.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,133,192,261,331,407,483,581,676", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "128,187,256,326,402,478,576,671,753"}, "to": {"startLines": "30,31,32,33,34,35,36,37,38", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "3382,3460,3519,3588,3658,3734,3810,3908,4003", "endColumns": "77,58,68,69,75,75,97,94,81", "endOffsets": "3455,3514,3583,3653,3729,3805,3903,3998,4080"}}]}]}