com.medicalcourses.app-jetified-credentials-1.2.0-rc01-0 C:\Users\<USER>\.gradle\caches\8.12\transforms\1203ed00ac77ef6e80766f7044873004\transformed\jetified-credentials-1.2.0-rc01\res
com.medicalcourses.app-browser-1.4.0-1 C:\Users\<USER>\.gradle\caches\8.12\transforms\2694643560e27514fec2bb7aa58bb43a\transformed\browser-1.4.0\res
com.medicalcourses.app-jetified-window-1.2.0-2 C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\res
com.medicalcourses.app-jetified-savedstate-1.2.1-3 C:\Users\<USER>\.gradle\caches\8.12\transforms\274b962bfc0ee3499460b07125b40653\transformed\jetified-savedstate-1.2.1\res
com.medicalcourses.app-lifecycle-livedata-core-2.7.0-4 C:\Users\<USER>\.gradle\caches\8.12\transforms\2e82a987f49d0f6484c5e02710942378\transformed\lifecycle-livedata-core-2.7.0\res
com.medicalcourses.app-jetified-core-1.0.0-5 C:\Users\<USER>\.gradle\caches\8.12\transforms\40dfcd36206381321d61bc4d0a504790\transformed\jetified-core-1.0.0\res
com.medicalcourses.app-jetified-startup-runtime-1.1.1-6 C:\Users\<USER>\.gradle\caches\8.12\transforms\506e321e672d66bb2b4486d2ee8beed8\transformed\jetified-startup-runtime-1.1.1\res
com.medicalcourses.app-jetified-lifecycle-viewmodel-savedstate-2.7.0-7 C:\Users\<USER>\.gradle\caches\8.12\transforms\51aab5cd0fa4197e7c50f04a365e85eb\transformed\jetified-lifecycle-viewmodel-savedstate-2.7.0\res
com.medicalcourses.app-jetified-credentials-play-services-auth-1.2.0-rc01-8 C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\res
com.medicalcourses.app-jetified-lifecycle-livedata-core-ktx-2.7.0-9 C:\Users\<USER>\.gradle\caches\8.12\transforms\661cfed71eb01d7f0c4bbaeae6faab4d\transformed\jetified-lifecycle-livedata-core-ktx-2.7.0\res
com.medicalcourses.app-jetified-core-common-2.0.3-10 C:\Users\<USER>\.gradle\caches\8.12\transforms\69fae46a4358823c36316c856fca8d84\transformed\jetified-core-common-2.0.3\res
com.medicalcourses.app-jetified-firebase-common-21.0.0-11 C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\res
com.medicalcourses.app-core-1.13.1-12 C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\res
com.medicalcourses.app-jetified-profileinstaller-1.3.1-13 C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\res
com.medicalcourses.app-jetified-media3-exoplayer-1.4.1-14 C:\Users\<USER>\.gradle\caches\8.12\transforms\8798035275722523399db6d5c2b9413f\transformed\jetified-media3-exoplayer-1.4.1\res
com.medicalcourses.app-jetified-window-java-1.2.0-15 C:\Users\<USER>\.gradle\caches\8.12\transforms\88ea6bbee0c3d252da1a135eb4f3ca19\transformed\jetified-window-java-1.2.0\res
com.medicalcourses.app-jetified-android-pdf-viewer-3.2.0-beta.3-16 C:\Users\<USER>\.gradle\caches\8.12\transforms\8dcd552472839f5c42dbe586449a488e\transformed\jetified-android-pdf-viewer-3.2.0-beta.3\res
com.medicalcourses.app-jetified-play-services-basement-18.4.0-17 C:\Users\<USER>\.gradle\caches\8.12\transforms\90b123502cca9071887a149ae2db5355\transformed\jetified-play-services-basement-18.4.0\res
com.medicalcourses.app-core-runtime-2.2.0-18 C:\Users\<USER>\.gradle\caches\8.12\transforms\9724ff6e8d4e6c9244bfb7005fae21c2\transformed\core-runtime-2.2.0\res
com.medicalcourses.app-jetified-activity-1.9.3-19 C:\Users\<USER>\.gradle\caches\8.12\transforms\aadc3611709663a05036e28336f6e814\transformed\jetified-activity-1.9.3\res
com.medicalcourses.app-jetified-play-services-base-18.1.0-20 C:\Users\<USER>\.gradle\caches\8.12\transforms\c1afa744a109aa55a636f9b508afdb55\transformed\jetified-play-services-base-18.1.0\res
com.medicalcourses.app-jetified-lifecycle-process-2.7.0-21 C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\res
com.medicalcourses.app-lifecycle-viewmodel-2.7.0-22 C:\Users\<USER>\.gradle\caches\8.12\transforms\c7d3b67c81f17a9d01acb41ef7dc29fd\transformed\lifecycle-viewmodel-2.7.0\res
com.medicalcourses.app-jetified-core-ktx-1.13.1-23 C:\Users\<USER>\.gradle\caches\8.12\transforms\d6a04692cb28023b65a80b358730356b\transformed\jetified-core-ktx-1.13.1\res
com.medicalcourses.app-lifecycle-livedata-2.7.0-24 C:\Users\<USER>\.gradle\caches\8.12\transforms\dc9487afb3090e92844f4382c0195afd\transformed\lifecycle-livedata-2.7.0\res
com.medicalcourses.app-jetified-tracing-1.2.0-25 C:\Users\<USER>\.gradle\caches\8.12\transforms\dcb95f8119ec5b1845af3693aa8a7063\transformed\jetified-tracing-1.2.0\res
com.medicalcourses.app-jetified-play-services-auth-20.7.0-26 C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\res
com.medicalcourses.app-fragment-1.7.1-27 C:\Users\<USER>\.gradle\caches\8.12\transforms\fe4c05eaffed710d6c61f97a8b8ac890\transformed\fragment-1.7.1\res
com.medicalcourses.app-jetified-annotation-experimental-1.4.0-28 C:\Users\<USER>\.gradle\caches\8.12\transforms\fe74d444a12af678d91d4bc25f88f447\transformed\jetified-annotation-experimental-1.4.0\res
com.medicalcourses.app-lifecycle-runtime-2.7.0-29 C:\Users\<USER>\.gradle\caches\8.12\transforms\fed144bef681fc8fb80134095669b372\transformed\lifecycle-runtime-2.7.0\res
com.medicalcourses.app-debug-30 E:\medcourses\medical_courses_app\android\app\src\debug\res
com.medicalcourses.app-main-31 E:\medcourses\medical_courses_app\android\app\src\main\res
com.medicalcourses.app-pngs-32 E:\medcourses\medical_courses_app\build\app\generated\res\pngs\debug
com.medicalcourses.app-res-33 E:\medcourses\medical_courses_app\build\app\generated\res\processDebugGoogleServices
com.medicalcourses.app-resValues-34 E:\medcourses\medical_courses_app\build\app\generated\res\resValues\debug
com.medicalcourses.app-packageDebugResources-35 E:\medcourses\medical_courses_app\build\app\intermediates\incremental\debug\packageDebugResources\merged.dir
com.medicalcourses.app-packageDebugResources-36 E:\medcourses\medical_courses_app\build\app\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.medicalcourses.app-debug-37 E:\medcourses\medical_courses_app\build\app\intermediates\merged_res\debug\mergeDebugResources
com.medicalcourses.app-debug-38 E:\medcourses\medical_courses_app\build\cloud_firestore\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-39 E:\medcourses\medical_courses_app\build\file_picker\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-40 E:\medcourses\medical_courses_app\build\firebase_auth\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-41 E:\medcourses\medical_courses_app\build\firebase_core\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-42 E:\medcourses\medical_courses_app\build\firebase_storage\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-43 E:\medcourses\medical_courses_app\build\flutter_pdfview\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-44 E:\medcourses\medical_courses_app\build\flutter_plugin_android_lifecycle\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-45 E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-46 E:\medcourses\medical_courses_app\build\package_info_plus\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-47 E:\medcourses\medical_courses_app\build\video_player_android\intermediates\packaged_res\debug\packageDebugResources
com.medicalcourses.app-debug-48 E:\medcourses\medical_courses_app\build\wakelock_plus\intermediates\packaged_res\debug\packageDebugResources
