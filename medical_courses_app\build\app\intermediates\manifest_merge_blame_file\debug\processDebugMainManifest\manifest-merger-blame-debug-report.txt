1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.medicalcourses.app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="36" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\medcourses\medical_courses_app\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->E:\medcourses\medical_courses_app\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!-- File picker permissions -->
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:3:5-79
17-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:3:22-77
18    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
18-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:4:5-80
18-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:4:22-78
19    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />
19-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:5:5-75
19-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:5:22-73
20    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO" />
20-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:6:5-74
20-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:6:22-72
21    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO" />
21-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:7:5-74
21-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:7:22-72
22    <!--
23 Required to query activities that can process text, see:
24         https://developer.android.com/training/package-visibility and
25         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
26
27         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
28    -->
29    <queries>
29-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:46:5-51:15
30        <intent>
30-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:47:9-50:18
31            <action android:name="android.intent.action.PROCESS_TEXT" />
31-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:48:13-72
31-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:48:21-70
32
33            <data android:mimeType="text/plain" />
33-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:49:13-50
33-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:49:19-48
34        </intent>
35        <intent>
35-->[:file_picker] E:\medcourses\medical_courses_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
36            <action android:name="android.intent.action.GET_CONTENT" />
36-->[:file_picker] E:\medcourses\medical_courses_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
36-->[:file_picker] E:\medcourses\medical_courses_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
37
38            <data android:mimeType="*/*" />
38-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:49:13-50
38-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:49:19-48
39        </intent>
40    </queries> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
41    <!-- <uses-sdk android:minSdkVersion="21" /> -->
42    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
42-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffaa8818dbd1091663198077660c8b10\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:10:5-79
42-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffaa8818dbd1091663198077660c8b10\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:10:22-76
43    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
43-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bae9c72dac47be60af4cceaead2e01ba\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:5-98
43-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\bae9c72dac47be60af4cceaead2e01ba\transformed\jetified-recaptcha-18.6.1\AndroidManifest.xml:9:22-95
44
45    <permission
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
46        android:name="com.medicalcourses.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.medicalcourses.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
50
51    <application
52        android:name="android.app.Application"
53        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
53-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
54        android:debuggable="true"
55        android:extractNativeLibs="false"
56        android:icon="@mipmap/ic_launcher"
57        android:label="medical_courses_app" >
58        <activity
59            android:name="com.medicalcourses.app.MainActivity"
60            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
61            android:exported="true"
62            android:hardwareAccelerated="true"
63            android:launchMode="singleTop"
64            android:taskAffinity=""
65            android:theme="@style/LaunchTheme"
66            android:windowSoftInputMode="adjustResize" >
67
68            <!--
69                 Specifies an Android theme to apply to this Activity as soon as
70                 the Android process has started. This theme is visible to the user
71                 while the Flutter UI initializes. After that, this theme continues
72                 to determine the Window background behind the Flutter UI.
73            -->
74            <meta-data
75                android:name="io.flutter.embedding.android.NormalTheme"
76                android:resource="@style/NormalTheme" />
77
78            <intent-filter>
79                <action android:name="android.intent.action.MAIN" />
80
81                <category android:name="android.intent.category.LAUNCHER" />
82            </intent-filter>
83        </activity>
84        <!--
85             Don't delete the meta-data below.
86             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
87        -->
88        <meta-data
89            android:name="flutterEmbedding"
90            android:value="2" />
91
92        <service
92-->[:cloud_firestore] E:\medcourses\medical_courses_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:19
93            android:name="com.google.firebase.components.ComponentDiscoveryService"
93-->[:cloud_firestore] E:\medcourses\medical_courses_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:18-89
94            android:directBootAware="true"
94-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:32:13-43
95            android:exported="false" >
95-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04d9165f91f2e7683fb0c85a67b27978\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:10:13-37
96            <meta-data
96-->[:cloud_firestore] E:\medcourses\medical_courses_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
97                android:name="com.google.firebase.components:io.flutter.plugins.firebase.firestore.FlutterFirebaseFirestoreRegistrar"
97-->[:cloud_firestore] E:\medcourses\medical_courses_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-134
98                android:value="com.google.firebase.components.ComponentRegistrar" />
98-->[:cloud_firestore] E:\medcourses\medical_courses_app\build\cloud_firestore\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
99            <meta-data
99-->[:firebase_auth] E:\medcourses\medical_courses_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
100                android:name="com.google.firebase.components:io.flutter.plugins.firebase.auth.FlutterFirebaseAuthRegistrar"
100-->[:firebase_auth] E:\medcourses\medical_courses_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
101                android:value="com.google.firebase.components.ComponentRegistrar" />
101-->[:firebase_auth] E:\medcourses\medical_courses_app\build\firebase_auth\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
102            <meta-data
102-->[:firebase_storage] E:\medcourses\medical_courses_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
103                android:name="com.google.firebase.components:io.flutter.plugins.firebase.storage.FlutterFirebaseAppRegistrar"
103-->[:firebase_storage] E:\medcourses\medical_courses_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-126
104                android:value="com.google.firebase.components.ComponentRegistrar" />
104-->[:firebase_storage] E:\medcourses\medical_courses_app\build\firebase_storage\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
105            <meta-data
105-->[:firebase_core] E:\medcourses\medical_courses_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-11:85
106                android:name="com.google.firebase.components:io.flutter.plugins.firebase.core.FlutterFirebaseCoreRegistrar"
106-->[:firebase_core] E:\medcourses\medical_courses_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:17-124
107                android:value="com.google.firebase.components.ComponentRegistrar" />
107-->[:firebase_core] E:\medcourses\medical_courses_app\build\firebase_core\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:17-82
108            <meta-data
108-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04d9165f91f2e7683fb0c85a67b27978\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:11:13-13:85
109                android:name="com.google.firebase.components:com.google.firebase.auth.ktx.FirebaseAuthLegacyRegistrar"
109-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04d9165f91f2e7683fb0c85a67b27978\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:12:17-119
110                android:value="com.google.firebase.components.ComponentRegistrar" />
110-->[com.google.firebase:firebase-auth-ktx:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\04d9165f91f2e7683fb0c85a67b27978\transformed\jetified-firebase-auth-ktx-23.2.1\AndroidManifest.xml:13:17-82
111            <meta-data
111-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0ebe7a3bfa64948066edf1c33518d34\transformed\jetified-firebase-firestore-ktx-25.1.4\AndroidManifest.xml:12:13-14:85
112                android:name="com.google.firebase.components:com.google.firebase.firestore.ktx.FirebaseFirestoreLegacyRegistrar"
112-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0ebe7a3bfa64948066edf1c33518d34\transformed\jetified-firebase-firestore-ktx-25.1.4\AndroidManifest.xml:13:17-129
113                android:value="com.google.firebase.components.ComponentRegistrar" />
113-->[com.google.firebase:firebase-firestore-ktx:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\e0ebe7a3bfa64948066edf1c33518d34\transformed\jetified-firebase-firestore-ktx-25.1.4\AndroidManifest.xml:14:17-82
114            <meta-data
114-->[com.google.firebase:firebase-storage-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c1c899a83ac3ca70e248e7617c19284\transformed\jetified-firebase-storage-ktx-21.0.2\AndroidManifest.xml:12:13-14:85
115                android:name="com.google.firebase.components:com.google.firebase.storage.ktx.FirebaseStorageLegacyRegistrar"
115-->[com.google.firebase:firebase-storage-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c1c899a83ac3ca70e248e7617c19284\transformed\jetified-firebase-storage-ktx-21.0.2\AndroidManifest.xml:13:17-125
116                android:value="com.google.firebase.components.ComponentRegistrar" />
116-->[com.google.firebase:firebase-storage-ktx:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c1c899a83ac3ca70e248e7617c19284\transformed\jetified-firebase-storage-ktx-21.0.2\AndroidManifest.xml:14:17-82
117            <meta-data
117-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffaa8818dbd1091663198077660c8b10\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:17:13-19:85
118                android:name="com.google.firebase.components:com.google.firebase.firestore.FirebaseFirestoreKtxRegistrar"
118-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffaa8818dbd1091663198077660c8b10\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:18:17-122
119                android:value="com.google.firebase.components.ComponentRegistrar" />
119-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffaa8818dbd1091663198077660c8b10\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:19:17-82
120            <meta-data
120-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffaa8818dbd1091663198077660c8b10\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:20:13-22:85
121                android:name="com.google.firebase.components:com.google.firebase.firestore.FirestoreRegistrar"
121-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffaa8818dbd1091663198077660c8b10\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:21:17-111
122                android:value="com.google.firebase.components.ComponentRegistrar" />
122-->[com.google.firebase:firebase-firestore:25.1.4] C:\Users\<USER>\.gradle\caches\8.12\transforms\ffaa8818dbd1091663198077660c8b10\transformed\jetified-firebase-firestore-25.1.4\AndroidManifest.xml:22:17-82
123            <meta-data
123-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:69:13-71:85
124                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
124-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:70:17-109
125                android:value="com.google.firebase.components.ComponentRegistrar" />
125-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:71:17-82
126            <meta-data
126-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cc0085658ff9b1b37a535d2f1dc144\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:30:13-32:85
127                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
127-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cc0085658ff9b1b37a535d2f1dc144\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:31:17-118
128                android:value="com.google.firebase.components.ComponentRegistrar" />
128-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cc0085658ff9b1b37a535d2f1dc144\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:32:17-82
129            <meta-data
129-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cc0085658ff9b1b37a535d2f1dc144\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:33:13-35:85
130                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
130-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cc0085658ff9b1b37a535d2f1dc144\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:34:17-107
131                android:value="com.google.firebase.components.ComponentRegistrar" />
131-->[com.google.firebase:firebase-storage:21.0.2] C:\Users\<USER>\.gradle\caches\8.12\transforms\b0cc0085658ff9b1b37a535d2f1dc144\transformed\jetified-firebase-storage-21.0.2\AndroidManifest.xml:35:17-82
132            <meta-data
132-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf8fa0f882bf3211744beb53ca0002b2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
133                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
133-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf8fa0f882bf3211744beb53ca0002b2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
134                android:value="com.google.firebase.components.ComponentRegistrar" />
134-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf8fa0f882bf3211744beb53ca0002b2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
135            <meta-data
135-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf8fa0f882bf3211744beb53ca0002b2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
136                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
136-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf8fa0f882bf3211744beb53ca0002b2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
137                android:value="com.google.firebase.components.ComponentRegistrar" />
137-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\cf8fa0f882bf3211744beb53ca0002b2\transformed\jetified-firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
138            <meta-data
138-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c803e57e6071aade91bb3e881d74f37\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
139                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
139-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c803e57e6071aade91bb3e881d74f37\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
140                android:value="com.google.firebase.components.ComponentRegistrar" />
140-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\7c803e57e6071aade91bb3e881d74f37\transformed\jetified-firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
141            <meta-data
141-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
142                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
142-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:36:17-109
143                android:value="com.google.firebase.components.ComponentRegistrar" />
143-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:37:17-82
144        </service>
145
146        <provider
146-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
147            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
147-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
148            android:authorities="com.medicalcourses.app.flutter.image_provider"
148-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
149            android:exported="false"
149-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
150            android:grantUriPermissions="true" >
150-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
151            <meta-data
151-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
152                android:name="android.support.FILE_PROVIDER_PATHS"
152-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
153                android:resource="@xml/flutter_image_picker_file_paths" />
153-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
154        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
155        <service
155-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
156            android:name="com.google.android.gms.metadata.ModuleDependencies"
156-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
157            android:enabled="false"
157-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
158            android:exported="false" >
158-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
159            <intent-filter>
159-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
160                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
160-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
160-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
161            </intent-filter>
162
163            <meta-data
163-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
164                android:name="photopicker_activity:0:required"
164-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
165                android:value="" />
165-->[:image_picker_android] E:\medcourses\medical_courses_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
166        </service>
167
168        <activity
168-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:29:9-46:20
169            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
169-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:30:13-80
170            android:excludeFromRecents="true"
170-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:31:13-46
171            android:exported="true"
171-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:32:13-36
172            android:launchMode="singleTask"
172-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:33:13-44
173            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
173-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:34:13-72
174            <intent-filter>
174-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:35:13-45:29
175                <action android:name="android.intent.action.VIEW" />
175-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
175-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
176
177                <category android:name="android.intent.category.DEFAULT" />
177-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
177-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
178                <category android:name="android.intent.category.BROWSABLE" />
178-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
178-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
179
180                <data
180-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:49:13-50
181                    android:host="firebase.auth"
182                    android:path="/"
183                    android:scheme="genericidp" />
184            </intent-filter>
185        </activity>
186        <activity
186-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:47:9-64:20
187            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
187-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:48:13-79
188            android:excludeFromRecents="true"
188-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:49:13-46
189            android:exported="true"
189-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:50:13-36
190            android:launchMode="singleTask"
190-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:51:13-44
191            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
191-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:52:13-72
192            <intent-filter>
192-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:53:13-63:29
193                <action android:name="android.intent.action.VIEW" />
193-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:17-69
193-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:36:25-66
194
195                <category android:name="android.intent.category.DEFAULT" />
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:17-76
195-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:38:27-73
196                <category android:name="android.intent.category.BROWSABLE" />
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:17-78
196-->[com.google.firebase:firebase-auth:23.2.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\590ec1d56d04cad85f0f66062dbacd2d\transformed\jetified-firebase-auth-23.2.1\AndroidManifest.xml:39:27-75
197
198                <data
198-->E:\medcourses\medical_courses_app\android\app\src\main\AndroidManifest.xml:49:13-50
199                    android:host="firebase.auth"
200                    android:path="/"
201                    android:scheme="recaptcha" />
202            </intent-filter>
203        </activity>
204
205        <provider
205-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
206            android:name="com.google.firebase.provider.FirebaseInitProvider"
206-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:24:13-77
207            android:authorities="com.medicalcourses.app.firebaseinitprovider"
207-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:25:13-72
208            android:directBootAware="true"
208-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:26:13-43
209            android:exported="false"
209-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:27:13-37
210            android:initOrder="100" />
210-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\6a7e3ef9232cbadb032af0fd5c6cc0ae\transformed\jetified-firebase-common-21.0.0\AndroidManifest.xml:28:13-36
211
212        <service
212-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
213            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
213-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
214            android:enabled="true"
214-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
215            android:exported="false" >
215-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
216            <meta-data
216-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
217                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
217-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
218                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
218-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
219        </service>
220
221        <activity
221-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
222            android:name="androidx.credentials.playservices.HiddenActivity"
222-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
223            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
223-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
224            android:enabled="true"
224-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
225            android:exported="false"
225-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
226            android:fitsSystemWindows="true"
226-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
227            android:theme="@style/Theme.Hidden" >
227-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.12\transforms\5f63a129f2be811d2c157fae892b3d5e\transformed\jetified-credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
228        </activity>
229        <activity
229-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:23:9-27:75
230            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
230-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:24:13-93
231            android:excludeFromRecents="true"
231-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:25:13-46
232            android:exported="false"
232-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:26:13-37
233            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
233-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:27:13-72
234        <!--
235            Service handling Google Sign-In user revocation. For apps that do not integrate with
236            Google Sign-In, this service will never be started.
237        -->
238        <service
238-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:33:9-37:51
239            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
239-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:34:13-89
240            android:exported="true"
240-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:35:13-36
241            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
241-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:36:13-107
242            android:visibleToInstantApps="true" />
242-->[com.google.android.gms:play-services-auth:20.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\f300e65a99bb18fd5c747a01a6536a4a\transformed\jetified-play-services-auth-20.7.0\AndroidManifest.xml:37:13-48
243
244        <activity
244-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c1afa744a109aa55a636f9b508afdb55\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
245            android:name="com.google.android.gms.common.api.GoogleApiActivity"
245-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c1afa744a109aa55a636f9b508afdb55\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:20:19-85
246            android:exported="false"
246-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c1afa744a109aa55a636f9b508afdb55\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:22:19-43
247            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
247-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c1afa744a109aa55a636f9b508afdb55\transformed\jetified-play-services-base-18.1.0\AndroidManifest.xml:21:19-78
248
249        <provider
249-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
250            android:name="androidx.startup.InitializationProvider"
250-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
251            android:authorities="com.medicalcourses.app.androidx-startup"
251-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
252            android:exported="false" >
252-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
253            <meta-data
253-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
254                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
254-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
255                android:value="androidx.startup" />
255-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
256            <meta-data
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
257                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
258                android:value="androidx.startup" />
258-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
259        </provider>
260
261        <uses-library
261-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
262            android:name="androidx.window.extensions"
262-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
263            android:required="false" />
263-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
264        <uses-library
264-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
265            android:name="androidx.window.sidecar"
265-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
266            android:required="false" />
266-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
267
268        <meta-data
268-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\90b123502cca9071887a149ae2db5355\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
269            android:name="com.google.android.gms.version"
269-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\90b123502cca9071887a149ae2db5355\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
270            android:value="@integer/google_play_services_version" />
270-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\90b123502cca9071887a149ae2db5355\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
271
272        <receiver
272-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
273            android:name="androidx.profileinstaller.ProfileInstallReceiver"
273-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
274            android:directBootAware="false"
274-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
275            android:enabled="true"
275-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
276            android:exported="true"
276-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
277            android:permission="android.permission.DUMP" >
277-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
278            <intent-filter>
278-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
279                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
279-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
280            </intent-filter>
281            <intent-filter>
281-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
282                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
282-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
283            </intent-filter>
284            <intent-filter>
284-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
285                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
285-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
286            </intent-filter>
287            <intent-filter>
287-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
288                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
288-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
288-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
289            </intent-filter>
290        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
291        <activity
291-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fae46a4358823c36316c856fca8d84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:14:9-18:65
292            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
292-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fae46a4358823c36316c856fca8d84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:15:13-93
293            android:exported="false"
293-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fae46a4358823c36316c856fca8d84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:16:13-37
294            android:stateNotNeeded="true"
294-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fae46a4358823c36316c856fca8d84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:17:13-42
295            android:theme="@style/Theme.PlayCore.Transparent" />
295-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.12\transforms\69fae46a4358823c36316c856fca8d84\transformed\jetified-core-common-2.0.3\AndroidManifest.xml:18:13-62
296    </application>
297
298</manifest>
