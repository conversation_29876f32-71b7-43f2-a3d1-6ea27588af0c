import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../utils/constants.dart';

/// شاشة تفاصيل الكورس
class CourseDetailsScreen extends StatelessWidget {
  final String courseId;

  const CourseDetailsScreen({super.key, required this.courseId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الكورس'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.pop();
          },
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.info_outline,
              size: 64,
              color: AppColors.primaryBlue,
            ),
            const SizedBox(height: AppDimensions.paddingM),
            const Text(
              'هذه شاشة تفاصيل الكورس',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.darkText,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            Text(
              'معرف الكورس: $courseId',
              style: const TextStyle(fontSize: 14, color: AppColors.lightText),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppDimensions.paddingS),
            const Text(
              'سيتم عرض تفاصيل الكورس والمحاضرات هنا',
              style: TextStyle(fontSize: 14, color: AppColors.lightText),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
