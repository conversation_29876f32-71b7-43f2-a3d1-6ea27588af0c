// android/app/build.gradle
// هذا هو ملف Gradle على مستوى التطبيق (الوحدة النمطية).

// تطبيق الإضافات (plugins) على هذا التطبيق النمطي
plugins {
    id("com.android.application")        // إضافة Android Application Plugin
    id("kotlin-android")                 // إضافة Kotlin Android Plugin
    id("dev.flutter.flutter-gradle-plugin") // إضافة Flutter Gradle Plugin

    // Firebase Google Services Plugin: تم تعطيله مؤقت<|im_start|> لحل مشاكل البناء
    // يجب أن يأتي بعد plugins الأخرى مثل `kotlin-android`
    // id("com.google.gms.google-services") // <<<<<< هذا السطر حاسم
}

android {
    // تحديد مساحة الاسم (namespace) لتطبيقك.
    // يجب أن يكون هذا مطابقًا لـ `applicationId` أدناه ولـ Bundle ID في Firebase.
    namespace = "com.medicalcourses.app" // <<<<< تم التعديل ليكون "com.medicalcourses.app" ليتوافق
                                          // مع `applicationId` في Firebase.
    
    // تحديد إصدار SDK لتجميع التطبيق.
    // بناءً على بيئتك، لديك Android SDK 36.0.0.
    compileSdk = 36 // <<<<< تم التعديل إلى 36
    
    // تحديد إصدار NDK إذا كنت تستخدم Native Development Kit (عادةً ما يحدده Flutter).
    // ndkVersion = flutter.ndkVersion // تم تعطيله مؤقت<|im_start|> لحل مشكلة NDK

    // إعدادات توافق Java للمترجم.
    // بيئتك تستخدم Java 17، لذا هذا الإعداد صحيح.
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    // إعدادات Kotlin الخاصة بالمترجم.
    // تحدد إصدار Java Virtual Machine المستهدف.
    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    // إعدادات التكوين الافتراضية لتطبيقك.
    defaultConfig {
        // معرف التطبيق (Application ID): يجب أن يطابق تمامًا ما أدخلته في Firebase Console.
        applicationId = "com.medicalcourses.app" // <<<<< تم التعديل إلى "com.medicalcourses.app"
        
        // الحد الأدنى لإصدار SDK المدعوم. 21 شائع ومتوافق على نطاق واسع.
        minSdk = 21 // <<<<< تم التعديل إلى 21 (بدلاً من flutter.minSdkVersion) لضمان التوافق
        // إصدار SDK المستهدف: يجب أن يتوافق مع `compileSdk` لضمان السلوك المتوقع.
        targetSdk = 36 // <<<<< تم التعديل إلى 36
        
        // أرقام إصدار التطبيق (يتم جلبها من pubspec.yaml).
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    // إعدادات أنواع البناء (Build Types) مثل 'release' و 'debug'.
    buildTypes {
        release {
            // TODO: أضف إعدادات التوقيع الخاصة بك لإصدارات الإنتاج هنا.
            // حاليًا، يتم التوقيع باستخدام مفاتيح Debug.
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

// ربط مشروع Flutter بمجلد الجذر للمشروع.
flutter {
    source = "../.."
}

// تعريف تبعيات المشروع (المكتبات الخارجية التي يستخدمها تطبيقك).
dependencies {
    // تبعيات Flutter الأساسية (لا تغيرها).
    // implementation(flutter.jar) // تم تعطيله مؤقت<|im_start|> لحل مشاكل البناء
    // implementation(platform(project(":flutter_shared_dependencies")))

    // Firebase BOM (Bill of Materials): تم تعطيله مؤقت<|im_start|> لحل مشاكل البناء
    // هذا السطر حاسم لضمان توافق جميع حزم Firebase مع بعضها البعض.
    // implementation(platform("com.google.firebase:firebase-bom:33.15.0")) // <<<<<< هذا السطر

    // تبعيات Firebase الخاصة بالخدمات التي ستستخدمها.
    // استخدام صيغة `-ktx` لأنك تستخدم Kotlin وهي موصى بها.
    // implementation("com.google.firebase:firebase-auth-ktx")     // لمصادقة المستخدمين
    // implementation("com.google.firebase:firebase-firestore-ktx") // لقاعدة بيانات NoSQL
    // implementation("com.google.firebase:firebase-storage-ktx")   // لتخزين الملفات (فيديوهات، صور، PDFs)
    // إذا كنت ستستخدم Cloud Functions من جهة العميل، أضف السطر التالي:
    // implementation("com.google.firebase:firebase-functions-ktx")

    // تبعيات Kotlin القياسية (لا تغيرها).
    implementation("org.jetbrains.kotlin:kotlin-stdlib-jdk8")

    // تبعيات الاختبار (لا تغيرها).
    testImplementation("junit:junit:4.13.2")
    androidTestImplementation("androidx.test.ext:junit:1.1.5")
    androidTestImplementation("androidx.test.espresso:espresso-core:3.5.1")
}
