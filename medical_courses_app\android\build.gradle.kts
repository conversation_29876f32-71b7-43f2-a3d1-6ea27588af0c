// android/build.gradle
// هذا هو ملف Gradle على مستوى المشروع.

buildscript {
    // تحديد إصدار Kotlin الذي سيُستخدم في جميع الوحدات النمطية
    // إصدار 1.9.0 مستقر ومتوافق مع بيئتك (JDK 17).
    ext.kotlin_version = '1.9.0'
    
    repositories {
        // مستودعات Google للوصول إلى حزم Android
        google()
        // المستودع المركزي لـ Maven لجلب المكتبات
        mavenCentral()
    }
    dependencies {
        // Android Gradle Plugin (AGP): مسؤول عن بناء تطبيقات Android
        // إصدار 8.1.1 (أو 8.2.0) متوافق مع Android SDK 36 و Java 17 لديك.
        classpath 'com.android.tools.build:gradle:8.1.1' // يمكن التحديث إلى 8.2.0 إذا أردت
        
        // Kotlin Gradle Plugin: مطلوب لبناء كود Kotlin
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

        // Firebase Google Services Plugin: هذا يُضاف كـ classpath هنا
        // يسمح لـ Gradle بمعالجة ملف `google-services.json`
        classpath 'com.google.gms:google-services:4.4.1' // هذا إصدار مستقر وموصى به
    }
}

allprojects {
    repositories {
        // التأكد من توفر المستودعات لكل الوحدات الفرعية
        google()
        mavenCentral()
    }
}

// إعداد مجلد بناء مخصص للمشروع. هذا الجزء يبدو صحيحًا من إعداداتك الحالية.
val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    // يضمن تقييم مشروع ':app' أولاً
    project.evaluationDependsOn(":app")
}

// مهمة 'clean' لحذف مجلدات البناء المؤقتة
tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
