// إعداد buildscript للمشروع
buildscript {
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        // Firebase Google Services Plugin: مطلوب لمعالجة ملف google-services.json
        classpath("com.google.gms:google-services:4.4.1")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}



// إعداد مجلد بناء مخصص للمشروع. هذا الجزء يبدو صحيحًا من إعداداتك الحالية.
val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    // يضمن تقييم مشروع ':app' أولاً
    project.evaluationDependsOn(":app")
}

// مهمة 'clean' لحذف مجلدات البناء المؤقتة
tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}
