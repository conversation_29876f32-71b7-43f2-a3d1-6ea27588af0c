import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:file_picker/file_picker.dart';
import '../../utils/constants.dart';
import '../../models/lecture_content.dart';
import '../../providers/admin_provider.dart';

/// نموذج إضافة/تعديل البطاقة التعليمية
class FlashcardFormDialog extends ConsumerStatefulWidget {
  final Flashcard? flashcard;
  final int? index;

  const FlashcardFormDialog({
    super.key,
    this.flashcard,
    this.index,
  });

  @override
  ConsumerState<FlashcardFormDialog> createState() =>
      _FlashcardFormDialogState();
}

class _FlashcardFormDialogState extends ConsumerState<FlashcardFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _questionController = TextEditingController();
  final _answerController = TextEditingController();

  bool _isImageType = false;
  PlatformFile? _selectedImage;
  String? _currentImageUrl;
  bool _isUploading = false;

  bool get isEditing => widget.flashcard != null;

  @override
  void initState() {
    super.initState();
    print('DEBUG: Flashcard Dialog Built - initState');

    if (isEditing) {
      _questionController.text = widget.flashcard!.question;
      _answerController.text = widget.flashcard!.answer;
      // تحديد نوع البطاقة بناءً على المحتوى
      _isImageType = widget.flashcard!.question.startsWith('http') ||
          widget.flashcard!.answer.startsWith('http');
      if (_isImageType) {
        _currentImageUrl = widget.flashcard!.question.startsWith('http')
            ? widget.flashcard!.question
            : widget.flashcard!.answer;
      }
    }
  }

  @override
  void dispose() {
    _questionController.dispose();
    _answerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    print('DEBUG: Flashcard Dialog Built - build method');
    return Dialog(
      child: IntrinsicHeight( // لضمان أن يأخذ الـ Column ارتفاعه الطبيعي
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.9,
            maxWidth: 700,
          ),
          child: Column(
            children: [
              // Header
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                decoration: const BoxDecoration(
                  color: AppColors.primaryBlue,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(AppDimensions.radiusM),
                    topRight: Radius.circular(AppDimensions.radiusM),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.quiz, color: AppColors.white),
                    const SizedBox(width: AppDimensions.paddingS),
                    Expanded(
                      child: Text(
                        isEditing
                            ? 'تعديل البطاقة التعليمية'
                            : 'إضافة بطاقة تعليمية جديدة',
                        style: const TextStyle(
                          color: AppColors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        print('DEBUG: Flashcard Dialog - Close button pressed');
                        Navigator.of(context).pop();
                      },
                      icon: const Icon(Icons.close, color: AppColors.white),
                    ),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(AppDimensions.paddingM),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // نوع البطاقة
                        const Text(
                          'نوع البطاقة:',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        const SizedBox(height: AppDimensions.paddingS),
                        SizedBox(
                          width: double.infinity,
                          child: SegmentedButton<bool>(
                            segments: const [
                              ButtonSegment<bool>(
                                value: false,
                                label: Text('نص'),
                                icon: Icon(Icons.text_fields),
                              ),
                              ButtonSegment<bool>(
                                value: true,
                                label: Text('صورة'),
                                icon: Icon(Icons.image),
                              ),
                            ],
                            selected: {_isImageType},
                            onSelectionChanged: (Set<bool> newSelection) {
                              setState(() {
                                _isImageType = newSelection.first;
                                // مسح البيانات عند تغيير النوع
                                _questionController.clear();
                                _answerController.clear();
                                _selectedImage = null;
                                _currentImageUrl = null;
                              });
                            },
                          ),
                        ),
                        const SizedBox(height: AppDimensions.paddingM),

                        if (_isImageType) ...[
                          // قسم الصورة
                          _buildImageSection(),
                        ] else ...[
                          // قسم النص
                          _buildTextSection(),
                        ],
                      ],
                    ),
                  ),
                ),
              ),

              // Actions
              Container(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                decoration: BoxDecoration(
                  color: AppColors.lightGray.withOpacity(0.1),
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(AppDimensions.radiusM),
                    bottomRight: Radius.circular(AppDimensions.radiusM),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {
                        print('DEBUG: Flashcard Dialog - Cancel button pressed');
                        Navigator.of(context).pop();
                      },
                      child: const Text('إلغاء'),
                    ),
                    const SizedBox(width: AppDimensions.paddingS),
                    SizedBox(
                      width: 100, // عرض ثابت لمنع مشاكل التخطيط
                      child: ElevatedButton(
                        onPressed: _isUploading ? null : _saveFlashcard,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryBlue,
                          foregroundColor: AppColors.white,
                        ),
                        child: _isUploading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.white),
                                ),
                              )
                            : Text(isEditing ? 'تحديث' : 'إضافة'),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextSection() {
    return Column(
      children: [
        TextFormField(
          controller: _questionController,
          decoration: const InputDecoration(
            labelText: 'السؤال *',
            hintText: 'اكتب السؤال هنا',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال السؤال';
            }
            return null;
          },
        ),
        const SizedBox(height: AppDimensions.paddingM),
        TextFormField(
          controller: _answerController,
          decoration: const InputDecoration(
            labelText: 'الإجابة *',
            hintText: 'اكتب الإجابة هنا',
            border: OutlineInputBorder(),
          ),
          maxLines: 3,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'يرجى إدخال الإجابة';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildImageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عرض الصورة المختارة أو الحالية
        if (_selectedImage != null || _currentImageUrl != null) ...[
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.lightGray),
              borderRadius: BorderRadius.circular(AppDimensions.radiusS),
            ),
            child: _selectedImage != null
                ? Image.memory(
                    _selectedImage!.bytes!,
                    fit: BoxFit.cover,
                  )
                : _currentImageUrl != null
                    ? Image.network(
                        _currentImageUrl!,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const Center(
                            child: Icon(
                              Icons.error,
                              color: AppColors.errorRed,
                              size: 50,
                            ),
                          );
                        },
                      )
                    : const Center(
                        child: Icon(
                          Icons.image,
                          color: AppColors.lightGray,
                          size: 50,
                        ),
                      ),
          ),
          const SizedBox(height: AppDimensions.paddingS),
        ],

        // أزرار اختيار الصورة
        const Text(
          'الصورة:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: AppDimensions.paddingS),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _pickImage,
                icon: const Icon(Icons.image),
                label: const Text('اختيار صورة'),
              ),
            ),
            if (_selectedImage != null || _currentImageUrl != null) ...[
              const SizedBox(width: AppDimensions.paddingS),
              IconButton(
                onPressed: () {
                  setState(() {
                    _selectedImage = null;
                    _currentImageUrl = null;
                  });
                },
                icon: const Icon(Icons.delete, color: AppColors.errorRed),
              ),
            ],
          ],
        ),
        const SizedBox(height: AppDimensions.paddingM),
        TextFormField(
          controller: _answerController,
          decoration: const InputDecoration(
            labelText: 'وصف الصورة (اختياري)',
            hintText: 'وصف أو تفسير للصورة',
            border: OutlineInputBorder(),
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  Future<void> _pickImage() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        
        // التحقق من حجم الملف (5 ميجابايت كحد أقصى)
        if (file.size > 5 * 1024 * 1024) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('حجم الصورة كبير جداً. الحد الأقصى 5 ميجابايت'),
                backgroundColor: AppColors.errorRed,
              ),
            );
          }
          return;
        }

        setState(() {
          _selectedImage = file;
          _currentImageUrl = null; // مسح الصورة الحالية
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في اختيار الصورة: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    }
  }

  Future<void> _saveFlashcard() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من وجود صورة في حالة النوع صورة
    if (_isImageType && _selectedImage == null && _currentImageUrl == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار صورة'),
          backgroundColor: AppColors.errorRed,
        ),
      );
      return;
    }

    setState(() {
      _isUploading = true;
    });

    try {
      String question = _questionController.text.trim();
      String answer = _answerController.text.trim();

      // رفع الصورة إذا تم اختيار صورة جديدة
      if (_isImageType && _selectedImage != null) {
        final imageId = DateTime.now().millisecondsSinceEpoch.toString();
        final imageUrl = await ref
            .read(lectureContentManagementProvider.notifier)
            .uploadImage(_selectedImage!, 'flashcards', imageId);

        // حفظ رابط الصورة في السؤال
        question = imageUrl;
      } else if (_isImageType && _currentImageUrl != null) {
        question = _currentImageUrl!;
      }

      final flashcard = Flashcard(
        question: question,
        answer: answer,
      );

      if (mounted) {
        Navigator.of(context).pop(flashcard);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ البطاقة: $e'),
            backgroundColor: AppColors.errorRed,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }
}
