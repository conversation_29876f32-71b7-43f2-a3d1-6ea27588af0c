import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../utils/constants.dart';
import '../../providers/admin_provider.dart';
import '../../models/year.dart';
import '../../widgets/admin/year_form_dialog.dart';

/// شاشة إدارة السنوات الدراسية
class ManageYearsScreen extends ConsumerWidget {
  const ManageYearsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final yearsAsync = ref.watch(yearsProvider);
    final yearManagement = ref.watch(yearManagementProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة السنوات الدراسية'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showYearDialog(context, ref),
          ),
        ],
      ),
      body: yearsAsync.when(
        data: (years) => _buildYearsList(context, ref, years),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.errorRed,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Text(
                'خطأ في تحميل السنوات',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              ElevatedButton(
                onPressed: () => ref.refresh(yearsProvider),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showYearDialog(context, ref),
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.add, color: AppColors.white),
      ),
    );
  }

  Widget _buildYearsList(
      BuildContext context, WidgetRef ref, List<Year> years) {
    if (years.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.calendar_today,
              size: 64,
              color: AppColors.lightText,
            ),
            SizedBox(height: AppDimensions.paddingM),
            Text(
              'لا توجد سنوات دراسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.lightText,
              ),
            ),
            SizedBox(height: AppDimensions.paddingS),
            Text(
              'اضغط على زر + لإضافة سنة جديدة',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      itemCount: years.length,
      itemBuilder: (context, index) {
        final year = years[index];
        return Card(
          margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: AppColors.primaryBlue,
              child: Text(
                '${year.order ?? index + 1}',
                style: const TextStyle(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              year.title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: year.description != null ? Text(year.description!) : null,
            trailing: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    _showYearDialog(context, ref, year: year);
                    break;
                  case 'subjects':
                    context.go('/admin/subjects/${year.id}');
                    break;
                  case 'delete':
                    _showDeleteDialog(context, ref, year);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('تعديل'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'subjects',
                  child: ListTile(
                    leading: Icon(Icons.subject),
                    title: Text('إدارة المواد'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: AppColors.errorRed),
                    title: Text('حذف',
                        style: TextStyle(color: AppColors.errorRed)),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
            onTap: () => context.go('/admin/subjects/${year.id}'),
          ),
        );
      },
    );
  }

  void _showYearDialog(BuildContext context, WidgetRef ref, {Year? year}) {
    showDialog(
      context: context,
      builder: (context) => YearFormDialog(year: year),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Year year) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
            'هل أنت متأكد من حذف السنة "${year.title}"؟\nسيتم حذف جميع المواد والكورسات المرتبطة بها.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref
                  .read(yearManagementProvider.notifier)
                  .deleteYear(year.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }
}
