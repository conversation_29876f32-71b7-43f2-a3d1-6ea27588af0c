import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج المحاضرة
class Lecture {
  final String id;
  final String title;
  final String? description;
  final String courseId;
  final int? order;
  final int? duration; // بالدقائق
  final bool hasVideo;
  final bool hasSummary;
  final bool hasFlashcards;
  final bool hasQuiz;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const Lecture({
    required this.id,
    required this.title,
    this.description,
    required this.courseId,
    this.order,
    this.duration,
    this.hasVideo = false,
    this.hasSummary = false,
    this.hasFlashcards = false,
    this.hasQuiz = false,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء Lecture من JSON
  factory Lecture.fromJson(Map<String, dynamic> json) {
    return Lecture(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String?,
      courseId: json['courseId'] as String,
      order: json['order'] as int?,
      duration: json['duration'] as int?,
      hasVideo: json['hasVideo'] as bool? ?? false,
      hasSummary: json['hasSummary'] as bool? ?? false,
      hasFlashcards: json['hasFlashcards'] as bool? ?? false,
      hasQuiz: json['hasQuiz'] as bool? ?? false,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: json['updatedAt'] != null 
          ? (json['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// إنشاء Lecture من DocumentSnapshot
  factory Lecture.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Lecture(
      id: doc.id,
      title: data['title'] as String,
      description: data['description'] as String?,
      courseId: data['courseId'] as String,
      order: data['order'] as int?,
      duration: data['duration'] as int?,
      hasVideo: data['hasVideo'] as bool? ?? false,
      hasSummary: data['hasSummary'] as bool? ?? false,
      hasFlashcards: data['hasFlashcards'] as bool? ?? false,
      hasQuiz: data['hasQuiz'] as bool? ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// تحويل Lecture إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'courseId': courseId,
      'order': order,
      'duration': duration,
      'hasVideo': hasVideo,
      'hasSummary': hasSummary,
      'hasFlashcards': hasFlashcards,
      'hasQuiz': hasQuiz,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// تحويل Lecture إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'courseId': courseId,
      'order': order,
      'duration': duration,
      'hasVideo': hasVideo,
      'hasSummary': hasSummary,
      'hasFlashcards': hasFlashcards,
      'hasQuiz': hasQuiz,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// إنشاء نسخة محدثة من Lecture
  Lecture copyWith({
    String? id,
    String? title,
    String? description,
    String? courseId,
    int? order,
    int? duration,
    bool? hasVideo,
    bool? hasSummary,
    bool? hasFlashcards,
    bool? hasQuiz,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Lecture(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      courseId: courseId ?? this.courseId,
      order: order ?? this.order,
      duration: duration ?? this.duration,
      hasVideo: hasVideo ?? this.hasVideo,
      hasSummary: hasSummary ?? this.hasSummary,
      hasFlashcards: hasFlashcards ?? this.hasFlashcards,
      hasQuiz: hasQuiz ?? this.hasQuiz,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Lecture(id: $id, title: $title, courseId: $courseId, order: $order)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Lecture && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
