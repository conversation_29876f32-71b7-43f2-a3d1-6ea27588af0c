import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../services/admin_service.dart';
import '../models/year.dart';
import '../models/subject.dart';
import '../models/course.dart';
import '../models/lecture.dart';
import '../models/lecture_content.dart';

// ==================== مزود الخدمة ====================

/// مزود خدمة المشرف
final adminServiceProvider = Provider<AdminService>((ref) {
  return AdminService();
});

// ==================== مزودات البيانات ====================

/// مزود قائمة السنوات
final yearsProvider = StreamProvider<List<Year>>((ref) {
  final adminService = ref.watch(adminServiceProvider);
  return adminService.getYears();
});

/// مزود قائمة المواد لسنة محددة
final subjectsProvider = StreamProvider.family<List<Subject>, String>((ref, yearId) {
  final adminService = ref.watch(adminServiceProvider);
  return adminService.getSubjects(yearId);
});

/// مزود قائمة الكورسات لمادة محددة
final coursesProvider = StreamProvider.family<List<Course>, String>((ref, subjectId) {
  final adminService = ref.watch(adminServiceProvider);
  return adminService.getCourses(subjectId);
});

/// مزود قائمة المحاضرات لكورس محدد
final lecturesProvider = StreamProvider.family<List<Lecture>, String>((ref, courseId) {
  final adminService = ref.watch(adminServiceProvider);
  return adminService.getLectures(courseId);
});

/// مزود محتوى المحاضرة
final lectureContentProvider = StreamProvider.family<LectureContent?, String>((ref, lectureId) {
  final adminService = ref.watch(adminServiceProvider);
  return adminService.getLectureContent(lectureId);
});

// ==================== مزودات الإدارة ====================

/// مزود إدارة السنوات
final yearManagementProvider = StateNotifierProvider<YearManagementNotifier, AsyncValue<void>>((ref) {
  final adminService = ref.watch(adminServiceProvider);
  return YearManagementNotifier(adminService);
});

/// مزود إدارة المواد
final subjectManagementProvider = StateNotifierProvider<SubjectManagementNotifier, AsyncValue<void>>((ref) {
  final adminService = ref.watch(adminServiceProvider);
  return SubjectManagementNotifier(adminService);
});

/// مزود إدارة الكورسات
final courseManagementProvider = StateNotifierProvider<CourseManagementNotifier, AsyncValue<void>>((ref) {
  final adminService = ref.watch(adminServiceProvider);
  return CourseManagementNotifier(adminService);
});

/// مزود إدارة المحاضرات
final lectureManagementProvider = StateNotifierProvider<LectureManagementNotifier, AsyncValue<void>>((ref) {
  final adminService = ref.watch(adminServiceProvider);
  return LectureManagementNotifier(adminService);
});

/// مزود إدارة محتوى المحاضرات
final lectureContentManagementProvider = StateNotifierProvider<LectureContentManagementNotifier, AsyncValue<void>>((ref) {
  final adminService = ref.watch(adminServiceProvider);
  return LectureContentManagementNotifier(adminService);
});

// ==================== كلاسات الإدارة ====================

/// إدارة السنوات
class YearManagementNotifier extends StateNotifier<AsyncValue<void>> {
  final AdminService _adminService;

  YearManagementNotifier(this._adminService) : super(const AsyncValue.data(null));

  /// إضافة سنة جديدة
  Future<void> addYear(Year year) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.addYear(year);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// تحديث سنة
  Future<void> updateYear(Year year) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.updateYear(year);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// حذف سنة
  Future<void> deleteYear(String id) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.deleteYear(id);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

/// إدارة المواد
class SubjectManagementNotifier extends StateNotifier<AsyncValue<void>> {
  final AdminService _adminService;

  SubjectManagementNotifier(this._adminService) : super(const AsyncValue.data(null));

  /// إضافة مادة جديدة
  Future<void> addSubject(Subject subject) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.addSubject(subject);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// تحديث مادة
  Future<void> updateSubject(Subject subject) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.updateSubject(subject);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// حذف مادة
  Future<void> deleteSubject(String id) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.deleteSubject(id);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

/// إدارة الكورسات
class CourseManagementNotifier extends StateNotifier<AsyncValue<void>> {
  final AdminService _adminService;

  CourseManagementNotifier(this._adminService) : super(const AsyncValue.data(null));

  /// إضافة كورس جديد
  Future<void> addCourse(Course course) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.addCourse(course);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// تحديث كورس
  Future<void> updateCourse(Course course) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.updateCourse(course);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// حذف كورس
  Future<void> deleteCourse(String id) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.deleteCourse(id);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

/// إدارة المحاضرات
class LectureManagementNotifier extends StateNotifier<AsyncValue<void>> {
  final AdminService _adminService;

  LectureManagementNotifier(this._adminService) : super(const AsyncValue.data(null));

  /// إضافة محاضرة جديدة
  Future<void> addLecture(Lecture lecture) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.addLecture(lecture);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// تحديث محاضرة
  Future<void> updateLecture(Lecture lecture) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.updateLecture(lecture);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// حذف محاضرة
  Future<void> deleteLecture(String id) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.deleteLecture(id);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

/// إدارة محتوى المحاضرات
class LectureContentManagementNotifier extends StateNotifier<AsyncValue<void>> {
  final AdminService _adminService;

  LectureContentManagementNotifier(this._adminService) : super(const AsyncValue.data(null));

  /// تحديث محتوى المحاضرة
  Future<void> updateLectureContent(LectureContent content) async {
    state = const AsyncValue.loading();
    try {
      await _adminService.updateLectureContent(content);
      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// رفع فيديو
  Future<String> uploadVideo(dynamic file, String lectureId) async {
    try {
      return await _adminService.uploadVideo(file, lectureId);
    } catch (e) {
      rethrow;
    }
  }

  /// رفع ملف PDF
  Future<String> uploadPDF(dynamic file, String lectureId) async {
    try {
      return await _adminService.uploadPDF(file, lectureId);
    } catch (e) {
      rethrow;
    }
  }

  /// رفع صورة
  Future<String> uploadImage(dynamic file, String folder, String fileName) async {
    try {
      return await _adminService.uploadImage(file, folder, fileName);
    } catch (e) {
      rethrow;
    }
  }
}
