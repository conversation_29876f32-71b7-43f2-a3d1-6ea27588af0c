import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../utils/constants.dart';
import '../../models/lecture_content.dart';

/// نموذج إضافة/تعديل سؤال الاختبار (مبسط)
class McqFormDialog extends ConsumerStatefulWidget {
  final QuizQuestion? question;
  final int? index;

  const McqFormDialog({
    super.key,
    this.question,
    this.index,
  });

  @override
  ConsumerState<McqFormDialog> createState() => _McqFormDialogState();
}

class _McqFormDialogState extends ConsumerState<McqFormDialog> {
  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: const BoxDecoration(
                color: AppColors.primaryBlue,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(AppDimensions.radiusM),
                  topRight: Radius.circular(AppDimensions.radiusM),
                ),
              ),
              child: Row(
                children: [
                  const Icon(Icons.assignment, color: AppColors.white),
                  const SizedBox(width: AppDimensions.paddingS),
                  const Expanded(
                    child: Text(
                      'إضافة سؤال اختبار',
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close, color: AppColors.white),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(AppDimensions.paddingM),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.construction,
                        size: 64,
                        color: AppColors.primaryBlue.withOpacity(0.5),
                      ),
                      const SizedBox(height: AppDimensions.paddingM),
                      Text(
                        'نموذج إضافة الأسئلة قيد التطوير',
                        style: TextStyle(
                          fontSize: 18,
                          color: AppColors.darkGray,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: AppDimensions.paddingS),
                      Text(
                        'سيتم إضافة هذه الميزة قريباً',
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.darkGray.withOpacity(0.7),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Actions
            Container(
              padding: const EdgeInsets.all(AppDimensions.paddingM),
              decoration: BoxDecoration(
                color: AppColors.lightGray.withOpacity(0.1),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(AppDimensions.radiusM),
                  bottomRight: Radius.circular(AppDimensions.radiusM),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  ElevatedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryBlue,
                      foregroundColor: AppColors.white,
                    ),
                    child: const Text('إغلاق'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
