import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج محتوى المحاضرة
class LectureContent {
  final String lectureId;
  final String? videoUrl;
  final String? summaryContent;
  final List<Flashcard> flashcards;
  final List<QuizQuestion> quiz;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const LectureContent({
    required this.lectureId,
    this.videoUrl,
    this.summaryContent,
    this.flashcards = const [],
    this.quiz = const [],
    this.createdAt,
    this.updatedAt,
  });

  /// إنشاء LectureContent من JSON
  factory LectureContent.fromJson(Map<String, dynamic> json) {
    return LectureContent(
      lectureId: json['lectureId'] as String,
      videoUrl: json['videoUrl'] as String?,
      summaryContent: json['summaryContent'] as String?,
      flashcards: (json['flashcards'] as List<dynamic>?)
          ?.map((item) => Flashcard.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      quiz: (json['quiz'] as List<dynamic>?)
          ?.map((item) => QuizQuestion.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: json['createdAt'] != null 
          ? (json['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: json['updatedAt'] != null 
          ? (json['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// إنشاء LectureContent من DocumentSnapshot
  factory LectureContent.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return LectureContent(
      lectureId: doc.id,
      videoUrl: data['videoUrl'] as String?,
      summaryContent: data['summaryContent'] as String?,
      flashcards: (data['flashcards'] as List<dynamic>?)
          ?.map((item) => Flashcard.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      quiz: (data['quiz'] as List<dynamic>?)
          ?.map((item) => QuizQuestion.fromJson(item as Map<String, dynamic>))
          .toList() ?? [],
      createdAt: data['createdAt'] != null 
          ? (data['createdAt'] as Timestamp).toDate()
          : null,
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// تحويل LectureContent إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'lectureId': lectureId,
      'videoUrl': videoUrl,
      'summaryContent': summaryContent,
      'flashcards': flashcards.map((card) => card.toJson()).toList(),
      'quiz': quiz.map((question) => question.toJson()).toList(),
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// تحويل LectureContent إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'videoUrl': videoUrl,
      'summaryContent': summaryContent,
      'flashcards': flashcards.map((card) => card.toJson()).toList(),
      'quiz': quiz.map((question) => question.toJson()).toList(),
      'createdAt': createdAt != null ? Timestamp.fromDate(createdAt!) : null,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// إنشاء نسخة محدثة من LectureContent
  LectureContent copyWith({
    String? lectureId,
    String? videoUrl,
    String? summaryContent,
    List<Flashcard>? flashcards,
    List<QuizQuestion>? quiz,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LectureContent(
      lectureId: lectureId ?? this.lectureId,
      videoUrl: videoUrl ?? this.videoUrl,
      summaryContent: summaryContent ?? this.summaryContent,
      flashcards: flashcards ?? this.flashcards,
      quiz: quiz ?? this.quiz,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'LectureContent(lectureId: $lectureId, hasVideo: ${videoUrl != null}, flashcardsCount: ${flashcards.length}, quizCount: ${quiz.length})';
  }
}

/// نموذج البطاقة التعليمية
class Flashcard {
  final String question;
  final String answer;

  const Flashcard({
    required this.question,
    required this.answer,
  });

  factory Flashcard.fromJson(Map<String, dynamic> json) {
    return Flashcard(
      question: json['question'] as String,
      answer: json['answer'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'answer': answer,
    };
  }
}

/// نموذج سؤال الاختبار
class QuizQuestion {
  final String question;
  final List<String> options;
  final int correctAnswer; // فهرس الإجابة الصحيحة
  final String? explanation;

  const QuizQuestion({
    required this.question,
    required this.options,
    required this.correctAnswer,
    this.explanation,
  });

  factory QuizQuestion.fromJson(Map<String, dynamic> json) {
    return QuizQuestion(
      question: json['question'] as String,
      options: List<String>.from(json['options'] as List),
      correctAnswer: json['correctAnswer'] as int,
      explanation: json['explanation'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'question': question,
      'options': options,
      'correctAnswer': correctAnswer,
      'explanation': explanation,
    };
  }
}
