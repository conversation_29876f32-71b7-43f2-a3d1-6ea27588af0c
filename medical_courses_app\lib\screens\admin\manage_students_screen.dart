import 'package:flutter/material.dart';
import '../../utils/constants.dart';

/// شاشة إدارة الطلاب
class ManageStudentsScreen extends StatelessWidget {
  const ManageStudentsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الطلاب'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.people,
              size: 64,
              color: AppColors.primaryBlue,
            ),
            SizedBox(height: AppDimensions.paddingM),
            Text(
              'هذه شاشة إدارة الطلاب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.darkText,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: AppDimensions.paddingS),
            Text(
              'سيتم عرض قائمة الطلاب وإدارتهم هنا',
              style: TextStyle(
                fontSize: 14,
                color: AppColors.lightText,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
