import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج الكورس
class Course {
  final String id;
  final String title;
  final String description;
  final String subjectId;
  final double price;
  final int? order;
  final String? imageUrl;
  final DateTime createdAt;
  final DateTime? updatedAt;

  const Course({
    required this.id,
    required this.title,
    required this.description,
    required this.subjectId,
    required this.price,
    this.order,
    this.imageUrl,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء Course من JSON
  factory Course.fromJson(Map<String, dynamic> json) {
    return Course(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      subjectId: json['subjectId'] as String,
      price: (json['price'] as num).toDouble(),
      order: json['order'] as int?,
      imageUrl: json['imageUrl'] as String?,
      createdAt: (json['createdAt'] as Timestamp).toDate(),
      updatedAt: json['updatedAt'] != null 
          ? (json['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// إنشاء Course من DocumentSnapshot
  factory Course.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Course(
      id: doc.id,
      title: data['title'] as String,
      description: data['description'] as String,
      subjectId: data['subjectId'] as String,
      price: (data['price'] as num).toDouble(),
      order: data['order'] as int?,
      imageUrl: data['imageUrl'] as String?,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate()
          : null,
    );
  }

  /// تحويل Course إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'subjectId': subjectId,
      'price': price,
      'order': order,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// تحويل Course إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'subjectId': subjectId,
      'price': price,
      'order': order,
      'imageUrl': imageUrl,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// إنشاء نسخة محدثة من Course
  Course copyWith({
    String? id,
    String? title,
    String? description,
    String? subjectId,
    double? price,
    int? order,
    String? imageUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Course(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      subjectId: subjectId ?? this.subjectId,
      price: price ?? this.price,
      order: order ?? this.order,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'Course(id: $id, title: $title, subjectId: $subjectId, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Course && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
