import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../utils/constants.dart';
import '../../providers/admin_provider.dart';
import '../../models/course.dart';
import '../../widgets/admin/course_form_dialog.dart';

/// شاشة إدارة الكورسات ضمن مادة
class ManageCoursesScreen extends ConsumerWidget {
  final String subjectId;

  const ManageCoursesScreen({super.key, required this.subjectId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final coursesAsync = ref.watch(coursesProvider(subjectId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الكورسات'),
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: AppColors.white,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            context.pop();
          },
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showCourseDialog(context, ref),
          ),
        ],
      ),
      body: coursesAsync.when(
        data: (courses) => _buildCoursesList(context, ref, courses),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stack) => Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 64,
                color: AppColors.errorRed,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              Text(
                'خطأ في تحميل الكورسات',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: AppDimensions.paddingS),
              Text(
                error.toString(),
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppDimensions.paddingM),
              ElevatedButton(
                onPressed: () => ref.refresh(coursesProvider(subjectId)),
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCourseDialog(context, ref),
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.add, color: AppColors.white),
      ),
    );
  }

  Widget _buildCoursesList(
    BuildContext context,
    WidgetRef ref,
    List<Course> courses,
  ) {
    if (courses.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.video_library, size: 64, color: AppColors.lightText),
            SizedBox(height: AppDimensions.paddingM),
            Text(
              'لا توجد كورسات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: AppColors.lightText,
              ),
            ),
            SizedBox(height: AppDimensions.paddingS),
            Text(
              'اضغط على زر + لإضافة كورس جديد',
              style: TextStyle(fontSize: 14, color: AppColors.lightText),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(AppDimensions.paddingM),
      itemCount: courses.length,
      itemBuilder: (context, index) {
        final course = courses[index];
        return _buildCourseCard(context, ref, course);
      },
    );
  }

  Widget _buildCourseCard(BuildContext context, WidgetRef ref, Course course) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.paddingM),
      elevation: 4,
      child: InkWell(
        onTap: () => context.go('/admin/lectures/${course.id}'),
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.paddingM),
          child: Row(
            children: [
              // صورة الكورس
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppDimensions.radiusS),
                  color: AppColors.lightGray.withValues(alpha: 0.3),
                ),
                child: course.imageUrl != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(
                          AppDimensions.radiusS,
                        ),
                        child: Image.network(
                          course.imageUrl!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return const Icon(
                              Icons.video_library,
                              size: 30,
                              color: AppColors.lightText,
                            );
                          },
                        ),
                      )
                    : const Icon(
                        Icons.video_library,
                        size: 30,
                        color: AppColors.lightText,
                      ),
              ),
              const SizedBox(width: AppDimensions.paddingM),
              // معلومات الكورس
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      course.title,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppDimensions.paddingXS),
                    Text(
                      course.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppColors.lightText,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: AppDimensions.paddingS),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppDimensions.paddingS,
                            vertical: AppDimensions.paddingXS,
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.medicalGreen.withValues(
                              alpha: 0.1,
                            ),
                            borderRadius: BorderRadius.circular(
                              AppDimensions.radiusS,
                            ),
                          ),
                          child: Text(
                            '${course.price.toStringAsFixed(0)} ريال',
                            style: const TextStyle(
                              color: AppColors.medicalGreen,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                        const SizedBox(width: AppDimensions.paddingS),
                        Text(
                          'ترتيب: ${course.order ?? 'غير محدد'}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: AppColors.lightText,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // قائمة الخيارات
              PopupMenuButton<String>(
                onSelected: (value) {
                  switch (value) {
                    case 'edit':
                      _showCourseDialog(context, ref, course: course);
                      break;
                    case 'lectures':
                      context.go('/admin/lectures/${course.id}');
                      break;
                    case 'delete':
                      _showDeleteDialog(context, ref, course);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'edit',
                    child: ListTile(
                      leading: Icon(Icons.edit),
                      title: Text('تعديل'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'lectures',
                    child: ListTile(
                      leading: Icon(Icons.play_lesson),
                      title: Text('إدارة المحاضرات'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: ListTile(
                      leading: Icon(Icons.delete, color: AppColors.errorRed),
                      title: Text(
                        'حذف',
                        style: TextStyle(color: AppColors.errorRed),
                      ),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCourseDialog(
    BuildContext context,
    WidgetRef ref, {
    Course? course,
  }) {
    showDialog(
      context: context,
      builder: (context) =>
          CourseFormDialog(course: course, subjectId: subjectId),
    );
  }

  void _showDeleteDialog(BuildContext context, WidgetRef ref, Course course) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف الكورس "${course.title}"؟\nسيتم حذف جميع المحاضرات المرتبطة به.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await ref
                  .read(courseManagementProvider.notifier)
                  .deleteCourse(course.id);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.errorRed,
            ),
            child: const Text('حذف', style: TextStyle(color: AppColors.white)),
          ),
        ],
      ),
    );
  }
}
