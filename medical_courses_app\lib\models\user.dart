import 'package:cloud_firestore/cloud_firestore.dart';

/// فئة User لتمثيل بيانات المستخدم في التطبيق و Firestore
class User {
  final String id; // معرف المستخدم (عادةً uid من Firebase Auth)
  final String name; // اسم المستخدم
  final String email; // البريد الإلكتروني للمستخدم
  final String role; // دور المستخدم (مثال: 'student', 'admin')
  final String? profileImageUrl; // رابط صورة الملف الشخصي (يمكن أن يكون null)
  final DateTime createdAt; // تاريخ إنشاء الحساب

  /// الباني (constructor) لفئة User
  User({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.profileImageUrl,
    required this.createdAt,
  });

  /// دالة factory لإنشاء كائن User من خريطة JSON (من Firestore)
  factory User.fromJson(Map<String, dynamic> json) {
    // التأكد من تحويل Timestamp إلى DateTime بشكل صحيح
    final Timestamp? createdAtTimestamp = json['createdAt'] as Timestamp?;
    final DateTime createdAtDate = createdAtTimestamp?.toDate() ?? DateTime.now();

    return User(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      role: json['role'] as String,
      profileImageUrl: json['profileImageUrl'] as String?,
      createdAt: createdAtDate,
    );
  }

  /// دالة لتحويل كائن User إلى خريطة Map لحفظه في Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'role': role,
      'profileImageUrl': profileImageUrl,
      'createdAt': Timestamp.fromDate(createdAt), // حفظ التاريخ كـ Timestamp
    };
  }

  /// دالة لنسخ كائن User مع إمكانية تحديث بعض الخصائص
  User copyWith({
    String? id,
    String? name,
    String? email,
    String? role,
    String? profileImageUrl,
    DateTime? createdAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      role: role ?? this.role,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// دالة للتحقق من كون المستخدم طالب
  bool get isStudent => role == 'student';

  /// دالة للتحقق من كون المستخدم مدير
  bool get isAdmin => role == 'admin';

  @override
  String toString() {
    return 'User(id: $id, name: $name, email: $email, role: $role)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
